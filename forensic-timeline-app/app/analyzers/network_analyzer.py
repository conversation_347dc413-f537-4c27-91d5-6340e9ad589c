"""
Network analysis components using <PERSON>ica<PERSON> and Zeek for offline PCAP processing.
"""

import os
import json
import subprocess
import tempfile
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..models.event import Event, EventType, EventSource, EventCategory
from ..config import Config

logger = logging.getLogger(__name__)

class NetworkAnalyzer:
    """
    Main network analyzer that orchestrates Suricata and Zeek analysis.
    """

    def __init__(self, config: Config):
        self.config = config
        self.suricata_analyzer = SuricataAnalyzer(config)
        self.zeek_analyzer = ZeekAnalyzer(config)

    def analyze_pcap(self, pcap_path: str, category: EventCategory,
                     output_dir: Optional[str] = None) -> List[Event]:
        """
        Analyze a PCAP file using both Suricata and Zeek.

        Args:
            pcap_path: Path to the PCAP file
            category: Whether this is baseline or attack data
            output_dir: Directory to store analysis results

        Returns:
            List of events extracted from the PCAP
        """
        if not os.path.exists(pcap_path):
            raise FileNotFoundError(f"PCAP file not found: {pcap_path}")

        if output_dir is None:
            output_dir = tempfile.mkdtemp(prefix="forensic_analysis_")

        logger.info(f"Starting network analysis of {pcap_path}")

        events = []

        try:
            # Run Suricata analysis
            logger.info("Running Suricata analysis...")
            suricata_events = self.suricata_analyzer.analyze(pcap_path, category, output_dir)
            events.extend(suricata_events)
            logger.info(f"Suricata generated {len(suricata_events)} events")

            # Run Zeek analysis
            logger.info("Running Zeek analysis...")
            zeek_events = self.zeek_analyzer.analyze(pcap_path, category, output_dir)
            events.extend(zeek_events)
            logger.info(f"Zeek generated {len(zeek_events)} events")

        except Exception as e:
            logger.error(f"Network analysis failed: {str(e)}")
            raise

        logger.info(f"Network analysis completed. Total events: {len(events)}")
        return events

class SuricataAnalyzer:
    """
    Suricata IDS analyzer for offline PCAP processing.
    """

    def __init__(self, config: Config):
        self.config = config
        self.suricata_path = config.SURICATA_PATH
        self.suricata_config = config.SURICATA_CONFIG

    def analyze(self, pcap_path: str, category: EventCategory, output_dir: str) -> List[Event]:
        """
        Run Suricata analysis on a PCAP file.

        Args:
            pcap_path: Path to the PCAP file
            category: Event category (baseline/attack)
            output_dir: Output directory for Suricata logs

        Returns:
            List of events from Suricata analysis
        """
        suricata_output_dir = os.path.join(output_dir, "suricata")
        os.makedirs(suricata_output_dir, exist_ok=True)

        # Suricata command for offline analysis
        cmd = [
            self.suricata_path,
            "-c", self.suricata_config,
            "-r", pcap_path,
            "-l", suricata_output_dir,
            "-v"
        ]

        logger.debug(f"Running Suricata command: {' '.join(cmd)}")

        try:
            # Run Suricata
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.config.ANALYSIS_TIMEOUT,
                check=True
            )

            logger.debug(f"Suricata stdout: {result.stdout}")
            if result.stderr:
                logger.warning(f"Suricata stderr: {result.stderr}")

        except subprocess.TimeoutExpired:
            logger.error(f"Suricata analysis timed out after {self.config.ANALYSIS_TIMEOUT} seconds")
            raise
        except subprocess.CalledProcessError as e:
            logger.error(f"Suricata analysis failed: {e}")
            logger.error(f"Suricata stderr: {e.stderr}")
            raise

        # Parse Suricata EVE JSON output
        eve_json_path = os.path.join(suricata_output_dir, "eve.json")
        return self._parse_eve_json(eve_json_path, category)

    def _parse_eve_json(self, eve_json_path: str, category: EventCategory) -> List[Event]:
        """
        Parse Suricata EVE JSON output into Event objects.

        Args:
            eve_json_path: Path to the eve.json file
            category: Event category

        Returns:
            List of Event objects
        """
        events = []

        if not os.path.exists(eve_json_path):
            logger.warning(f"Suricata EVE JSON file not found: {eve_json_path}")
            return events

        try:
            with open(eve_json_path, 'r') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        eve_event = json.loads(line)
                        event = self._convert_eve_event(eve_event, category)
                        if event:
                            events.append(event)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse JSON line {line_num} in {eve_json_path}: {e}")
                        continue
                    except Exception as e:
                        logger.warning(f"Failed to convert EVE event on line {line_num}: {e}")
                        continue

        except Exception as e:
            logger.error(f"Failed to read EVE JSON file {eve_json_path}: {e}")
            raise

        logger.info(f"Parsed {len(events)} events from Suricata EVE JSON")
        return events

    def _convert_eve_event(self, eve_event: Dict[str, Any], category: EventCategory) -> Optional[Event]:
        """
        Convert a Suricata EVE JSON event to our Event model.

        Args:
            eve_event: Raw EVE JSON event
            category: Event category

        Returns:
            Event object or None if conversion fails
        """
        try:
            # Parse timestamp
            timestamp_str = eve_event.get('timestamp')
            if not timestamp_str:
                return None

            # Handle different timestamp formats
            if timestamp_str.endswith('+0000'):
                # Convert +0000 to +00:00 format
                timestamp_str = timestamp_str[:-5] + '+00:00'
            elif timestamp_str.endswith('Z'):
                # Convert Z to +00:00 format
                timestamp_str = timestamp_str[:-1] + '+00:00'

            timestamp = datetime.fromisoformat(timestamp_str)

            # Determine event type based on EVE event type
            eve_event_type = eve_event.get('event_type', 'unknown')
            event_type = self._map_eve_event_type(eve_event_type)

            # Extract basic information
            src_ip = eve_event.get('src_ip', '')
            dest_ip = eve_event.get('dest_ip', '')
            src_port = eve_event.get('src_port', 0)
            dest_port = eve_event.get('dest_port', 0)
            proto = eve_event.get('proto', '')

            # Build description based on event type
            if eve_event_type == 'alert':
                alert = eve_event.get('alert', {})
                signature = alert.get('signature', 'Unknown alert')
                description = f"Suricata Alert: {signature}"
                severity = alert.get('severity', 3)
            elif eve_event_type == 'http':
                http = eve_event.get('http', {})
                hostname = http.get('hostname', dest_ip)
                url = http.get('url', '/')
                description = f"HTTP Request: {hostname}{url}"
                severity = None
            elif eve_event_type == 'dns':
                dns = eve_event.get('dns', {})
                query = dns.get('rrname', 'unknown')
                description = f"DNS Query: {query}"
                severity = None
            elif eve_event_type == 'tls':
                tls = eve_event.get('tls', {})
                sni = tls.get('sni', dest_ip)
                description = f"TLS Connection: {sni}"
                severity = None
            else:
                description = f"Network Event: {eve_event_type} {src_ip}:{src_port} -> {dest_ip}:{dest_port}"
                severity = None

            # Build metadata
            metadata = {
                'src_ip': src_ip,
                'dst_ip': dest_ip,
                'src_port': src_port,
                'dst_port': dest_port,
                'protocol': proto,
                'eve_event_type': eve_event_type
            }

            # Add event-specific metadata
            if eve_event_type == 'alert':
                alert = eve_event.get('alert', {})
                metadata.update({
                    'signature_id': alert.get('signature_id'),
                    'signature': alert.get('signature'),
                    'category': alert.get('category'),
                    'severity': alert.get('severity'),
                    'gid': alert.get('gid'),
                    'rev': alert.get('rev')
                })
            elif eve_event_type == 'http':
                http = eve_event.get('http', {})
                metadata.update({
                    'hostname': http.get('hostname'),
                    'url': http.get('url'),
                    'http_method': http.get('http_method'),
                    'status': http.get('status'),
                    'user_agent': http.get('http_user_agent')
                })
            elif eve_event_type == 'dns':
                dns = eve_event.get('dns', {})
                metadata.update({
                    'query': dns.get('rrname'),
                    'query_type': dns.get('rrtype'),
                    'response_code': dns.get('rcode'),
                    'answers': dns.get('answers', [])
                })

            # Create Event object
            event = Event(
                timestamp=timestamp,
                source=EventSource.SURICATA,
                event_type=event_type,
                category=category,
                description=description,
                severity=self._map_severity(severity) if severity is not None else None,
                metadata=metadata
            )

            return event

        except Exception as e:
            logger.warning(f"Failed to convert EVE event: {e}")
            logger.debug(f"Problematic EVE event: {eve_event}")
            return None

    def _map_eve_event_type(self, eve_event_type: str) -> EventType:
        """Map Suricata EVE event types to our EventType enum."""
        mapping = {
            'alert': EventType.NETWORK_ALERT,
            'http': EventType.HTTP_REQUEST,
            'dns': EventType.DNS_QUERY,
            'tls': EventType.NETWORK_CONNECTION,
            'ssh': EventType.NETWORK_CONNECTION,
            'flow': EventType.NETWORK_CONNECTION,
            'netflow': EventType.NETWORK_CONNECTION,
            'files': EventType.FILE_TRANSFER
        }
        return mapping.get(eve_event_type, EventType.NETWORK_CONNECTION)

    def _map_severity(self, severity: int) -> str:
        """Map Suricata severity levels to string."""
        mapping = {
            1: "high",
            2: "medium",
            3: "low",
            4: "low"
        }
        return mapping.get(severity, "medium")

class ZeekAnalyzer:
    """
    Zeek network analyzer for offline PCAP processing.
    """

    def __init__(self, config: Config):
        self.config = config
        self.zeek_path = config.ZEEK_PATH

    def analyze(self, pcap_path: str, category: EventCategory, output_dir: str) -> List[Event]:
        """
        Run Zeek analysis on a PCAP file.

        Args:
            pcap_path: Path to the PCAP file
            category: Event category (baseline/attack)
            output_dir: Output directory for Zeek logs

        Returns:
            List of events from Zeek analysis
        """
        zeek_output_dir = os.path.join(output_dir, "zeek")
        os.makedirs(zeek_output_dir, exist_ok=True)

        # Zeek command for offline analysis
        cmd = [
            self.zeek_path,
            "-r", pcap_path,
            "-C"  # Ignore checksums
        ]

        logger.debug(f"Running Zeek command: {' '.join(cmd)}")

        try:
            # Run Zeek
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.config.ANALYSIS_TIMEOUT,
                cwd=zeek_output_dir,
                check=True
            )

            logger.debug(f"Zeek stdout: {result.stdout}")
            if result.stderr:
                logger.warning(f"Zeek stderr: {result.stderr}")

        except subprocess.TimeoutExpired:
            logger.error(f"Zeek analysis timed out after {self.config.ANALYSIS_TIMEOUT} seconds")
            raise
        except subprocess.CalledProcessError as e:
            logger.error(f"Zeek analysis failed: {e}")
            logger.error(f"Zeek stderr: {e.stderr}")
            raise

        # Parse Zeek log files
        return self._parse_zeek_logs(zeek_output_dir, category)

    def _parse_zeek_logs(self, zeek_output_dir: str, category: EventCategory) -> List[Event]:
        """
        Parse Zeek log files into Event objects.

        Args:
            zeek_output_dir: Directory containing Zeek logs
            category: Event category

        Returns:
            List of Event objects
        """
        events = []

        # Priority log files for forensic analysis
        priority_logs = [
            'notice.log',    # Zeek notices (anomalies, policy violations)
            'weird.log',     # Protocol anomalies
            'intel.log',     # Intelligence matches
            'signatures.log', # Signature matches
            'files.log',     # File transfers
            'dns.log',       # DNS queries
            'http.log',      # HTTP requests
            'conn.log'       # Network connections (sample only)
        ]

        for log_file in priority_logs:
            log_path = os.path.join(zeek_output_dir, log_file)
            if os.path.exists(log_path):
                try:
                    log_events = self._parse_zeek_log_file(log_path, log_file, category)
                    events.extend(log_events)
                    logger.debug(f"Parsed {len(log_events)} events from {log_file}")
                except Exception as e:
                    logger.warning(f"Failed to parse Zeek log {log_file}: {e}")

        logger.info(f"Parsed {len(events)} events from Zeek logs")
        return events

    def _parse_zeek_log_file(self, log_path: str, log_type: str, category: EventCategory) -> List[Event]:
        """
        Parse a specific Zeek log file.

        Args:
            log_path: Path to the log file
            log_type: Type of log (e.g., 'notice.log', 'dns.log')
            category: Event category

        Returns:
            List of Event objects
        """
        events = []

        try:
            with open(log_path, 'r') as f:
                lines = f.readlines()

            # Skip header lines and parse data
            data_lines = [line for line in lines if not line.startswith('#')]

            # Get field names from header
            field_names = self._extract_zeek_fields(lines)

            for line in data_lines:
                line = line.strip()
                if not line:
                    continue

                try:
                    event = self._parse_zeek_log_line(line, field_names, log_type, category)
                    if event:
                        events.append(event)
                except Exception as e:
                    logger.debug(f"Failed to parse Zeek log line: {e}")
                    continue

        except Exception as e:
            logger.error(f"Failed to read Zeek log file {log_path}: {e}")
            raise

        return events

    def _extract_zeek_fields(self, lines: List[str]) -> List[str]:
        """Extract field names from Zeek log header."""
        for line in lines:
            if line.startswith('#fields'):
                return line.strip().split('\t')[1:]  # Skip '#fields' prefix
        return []

    def _parse_zeek_log_line(self, line: str, field_names: List[str],
                            log_type: str, category: EventCategory) -> Optional[Event]:
        """
        Parse a single Zeek log line into an Event object.

        Args:
            line: Log line to parse
            field_names: Field names from log header
            log_type: Type of Zeek log
            category: Event category

        Returns:
            Event object or None
        """
        try:
            fields = line.split('\t')
            if len(fields) != len(field_names):
                return None

            # Create field dictionary
            log_data = dict(zip(field_names, fields))

            # Parse timestamp (first field is usually 'ts')
            timestamp_str = log_data.get('ts', '')
            if not timestamp_str or timestamp_str == '-':
                return None

            timestamp = datetime.fromtimestamp(float(timestamp_str))

            # Determine event type and build description
            event_type, description = self._map_zeek_log_type(log_type, log_data)

            # Extract network indicators
            metadata = {
                'zeek_log_type': log_type.replace('.log', ''),
                'orig_h': log_data.get('id.orig_h', log_data.get('orig_h', '')),
                'resp_h': log_data.get('id.resp_h', log_data.get('resp_h', '')),
                'orig_p': log_data.get('id.orig_p', log_data.get('orig_p', '')),
                'resp_p': log_data.get('id.resp_p', log_data.get('resp_p', ''))
            }

            # Add log-specific metadata
            if log_type == 'notice.log':
                metadata.update({
                    'note': log_data.get('note', ''),
                    'msg': log_data.get('msg', ''),
                    'sub': log_data.get('sub', ''),
                    'actions': log_data.get('actions', '')
                })
            elif log_type == 'dns.log':
                metadata.update({
                    'query': log_data.get('query', ''),
                    'qtype': log_data.get('qtype', ''),
                    'rcode': log_data.get('rcode', ''),
                    'answers': log_data.get('answers', '')
                })
            elif log_type == 'http.log':
                metadata.update({
                    'method': log_data.get('method', ''),
                    'host': log_data.get('host', ''),
                    'uri': log_data.get('uri', ''),
                    'status_code': log_data.get('status_code', ''),
                    'user_agent': log_data.get('user_agent', '')
                })
            elif log_type == 'files.log':
                metadata.update({
                    'filename': log_data.get('filename', ''),
                    'mime_type': log_data.get('mime_type', ''),
                    'md5': log_data.get('md5', ''),
                    'sha1': log_data.get('sha1', ''),
                    'source': log_data.get('source', '')
                })

            # Determine severity based on log type and content
            severity = self._determine_zeek_severity(log_type, log_data)

            event = Event(
                timestamp=timestamp,
                source=EventSource.ZEEK,
                event_type=event_type,
                category=category,
                description=description,
                severity=severity,
                metadata=metadata
            )

            return event

        except Exception as e:
            logger.debug(f"Failed to parse Zeek log line: {e}")
            return None

    def _map_zeek_log_type(self, log_type: str, log_data: Dict[str, str]) -> tuple:
        """Map Zeek log type to EventType and generate description."""
        if log_type == 'notice.log':
            note = log_data.get('note', 'Unknown')
            msg = log_data.get('msg', '')
            description = f"Zeek Notice: {note} - {msg}"
            return EventType.NETWORK_ALERT, description

        elif log_type == 'weird.log':
            name = log_data.get('name', 'Unknown')
            description = f"Zeek Protocol Anomaly: {name}"
            return EventType.NETWORK_ALERT, description

        elif log_type == 'dns.log':
            query = log_data.get('query', 'unknown')
            description = f"DNS Query: {query}"
            return EventType.DNS_QUERY, description

        elif log_type == 'http.log':
            host = log_data.get('host', log_data.get('resp_h', 'unknown'))
            uri = log_data.get('uri', '/')
            description = f"HTTP Request: {host}{uri}"
            return EventType.HTTP_REQUEST, description

        elif log_type == 'files.log':
            filename = log_data.get('filename', 'unknown')
            description = f"File Transfer: {filename}"
            return EventType.FILE_TRANSFER, description

        else:
            orig_h = log_data.get('id.orig_h', log_data.get('orig_h', ''))
            resp_h = log_data.get('id.resp_h', log_data.get('resp_h', ''))
            description = f"Network Connection: {orig_h} -> {resp_h}"
            return EventType.NETWORK_CONNECTION, description

    def _determine_zeek_severity(self, log_type: str, log_data: Dict[str, str]) -> Optional[str]:
        """Determine severity based on Zeek log type and content."""
        if log_type in ['notice.log', 'weird.log', 'intel.log', 'signatures.log']:
            return "medium"  # Zeek notices are generally noteworthy

        # Check for suspicious indicators
        if log_type == 'dns.log':
            query = log_data.get('query', '').lower()
            if any(suspicious in query for suspicious in ['dga', 'malware', 'botnet', 'c2']):
                return "high"

        elif log_type == 'http.log':
            user_agent = log_data.get('user_agent', '').lower()
            if 'bot' in user_agent or 'crawler' in user_agent:
                return "low"

        return None  # No specific severity