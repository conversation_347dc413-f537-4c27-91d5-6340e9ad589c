"""
Baseline vs Attack comparison engine for forensic timeline analysis.
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Set, Tuple, Optional
from collections import defaultdict, Counter
import logging
import ipaddress
import re

from ..models.event import Event, EventCategory, EventSource, EventType

logger = logging.getLogger(__name__)

class ComparisonResult:
    """Represents the result of comparing baseline and attack scenarios."""
    
    def __init__(self):
        self.baseline_events: List[Event] = []
        self.attack_events: List[Event] = []
        self.unique_attack_indicators: Dict[str, Set[str]] = {}
        self.common_indicators: Dict[str, Set[str]] = {}
        self.suspicious_patterns: List[Dict[str, Any]] = []
        self.timeline_anomalies: List[Dict[str, Any]] = []
        self.severity_changes: Dict[str, Dict[str, int]] = {}
        self.new_event_types: Set[str] = set()
        self.comparison_summary: Dict[str, Any] = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert comparison result to dictionary."""
        return {
            'baseline_event_count': len(self.baseline_events),
            'attack_event_count': len(self.attack_events),
            'unique_attack_indicators': {k: list(v) for k, v in self.unique_attack_indicators.items()},
            'common_indicators': {k: list(v) for k, v in self.common_indicators.items()},
            'suspicious_patterns': self.suspicious_patterns,
            'timeline_anomalies': self.timeline_anomalies,
            'severity_changes': self.severity_changes,
            'new_event_types': list(self.new_event_types),
            'comparison_summary': self.comparison_summary
        }

class BaselineAttackComparison:
    """
    Engine for comparing baseline and attack scenarios to identify differences,
    anomalies, and indicators of compromise.
    """
    
    def __init__(self):
        self.suspicious_keywords = [
            'powershell', 'cmd', 'bash', 'wget', 'curl', 'nc', 'netcat',
            'mimikatz', 'hashdump', 'password', 'credential', 'token',
            'backdoor', 'payload', 'exploit', 'reverse', 'shell',
            'persistence', 'privilege', 'escalation', 'lateral',
            'exfiltration', 'beacon', 'c2', 'command', 'control'
        ]
        
        self.network_anomaly_thresholds = {
            'new_destinations': 5,  # Number of new IPs to flag as suspicious
            'port_scanning': 10,    # Number of different ports to flag as scanning
            'data_volume': 1000000  # Bytes threshold for large transfers
        }
    
    def compare_scenarios(self, baseline_events: List[Event], 
                         attack_events: List[Event]) -> ComparisonResult:
        """
        Compare baseline and attack scenarios to identify differences and anomalies.
        
        Args:
            baseline_events: Events from baseline scenario
            attack_events: Events from attack scenario
            
        Returns:
            ComparisonResult with detailed analysis
        """
        logger.info(f"Comparing {len(baseline_events)} baseline events with {len(attack_events)} attack events")
        
        result = ComparisonResult()
        result.baseline_events = baseline_events
        result.attack_events = attack_events
        
        # Extract indicators from both scenarios
        baseline_indicators = self._extract_indicators(baseline_events)
        attack_indicators = self._extract_indicators(attack_events)
        
        # Find unique attack indicators
        result.unique_attack_indicators = self._find_unique_indicators(
            baseline_indicators, attack_indicators
        )
        
        # Find common indicators
        result.common_indicators = self._find_common_indicators(
            baseline_indicators, attack_indicators
        )
        
        # Detect suspicious patterns
        result.suspicious_patterns = self._detect_suspicious_patterns(attack_events)
        
        # Analyze timeline anomalies
        result.timeline_anomalies = self._analyze_timeline_anomalies(
            baseline_events, attack_events
        )
        
        # Compare severity distributions
        result.severity_changes = self._compare_severity_distributions(
            baseline_events, attack_events
        )
        
        # Find new event types
        result.new_event_types = self._find_new_event_types(
            baseline_events, attack_events
        )
        
        # Generate comparison summary
        result.comparison_summary = self._generate_comparison_summary(result)
        
        logger.info(f"Comparison complete: found {len(result.unique_attack_indicators)} unique indicator types")
        
        return result
    
    def _extract_indicators(self, events: List[Event]) -> Dict[str, Set[str]]:
        """Extract various indicators from events."""
        indicators = {
            'ip_addresses': set(),
            'domains': set(),
            'processes': set(),
            'users': set(),
            'files': set(),
            'commands': set(),
            'ports': set(),
            'protocols': set()
        }
        
        for event in events:
            metadata = event.metadata or {}
            
            # Extract IP addresses
            for ip_field in ['src_ip', 'dst_ip', 'source_ip', 'destination_ip', 'client_ip', 'server_ip']:
                if ip_field in metadata and metadata[ip_field]:
                    ip = metadata[ip_field]
                    if self._is_valid_ip(ip):
                        indicators['ip_addresses'].add(ip)
            
            # Extract domains
            for domain_field in ['domain', 'hostname', 'dns_query', 'http_host']:
                if domain_field in metadata and metadata[domain_field]:
                    domain = metadata[domain_field].lower()
                    if self._is_valid_domain(domain):
                        indicators['domains'].add(domain)
            
            # Extract processes
            for proc_field in ['process_name', 'proc_name', 'executable', 'image']:
                if proc_field in metadata and metadata[proc_field]:
                    process = metadata[proc_field].split('/')[-1].split('\\')[-1].lower()
                    indicators['processes'].add(process)
            
            # Extract users
            for user_field in ['username', 'user', 'account', 'account_name']:
                if user_field in metadata and metadata[user_field]:
                    user = metadata[user_field].lower()
                    indicators['users'].add(user)
            
            # Extract files
            for file_field in ['filename', 'file_path', 'target_filename']:
                if file_field in metadata and metadata[file_field]:
                    filename = metadata[file_field].split('/')[-1].split('\\')[-1].lower()
                    indicators['files'].add(filename)
            
            # Extract commands
            for cmd_field in ['command', 'cmd', 'commandline']:
                if cmd_field in metadata and metadata[cmd_field]:
                    command = metadata[cmd_field].lower()
                    indicators['commands'].add(command)
            
            # Extract ports
            for port_field in ['src_port', 'dst_port', 'source_port', 'destination_port']:
                if port_field in metadata and metadata[port_field]:
                    try:
                        port = int(metadata[port_field])
                        if 1 <= port <= 65535:
                            indicators['ports'].add(str(port))
                    except (ValueError, TypeError):
                        pass
            
            # Extract protocols
            if 'protocol' in metadata and metadata['protocol']:
                protocol = metadata['protocol'].upper()
                indicators['protocols'].add(protocol)
        
        return indicators
    
    def _find_unique_indicators(self, baseline_indicators: Dict[str, Set[str]], 
                               attack_indicators: Dict[str, Set[str]]) -> Dict[str, Set[str]]:
        """Find indicators that appear only in attack scenario."""
        unique_indicators = {}
        
        for indicator_type in baseline_indicators:
            baseline_set = baseline_indicators[indicator_type]
            attack_set = attack_indicators.get(indicator_type, set())
            
            # Find indicators unique to attack
            unique_to_attack = attack_set - baseline_set
            if unique_to_attack:
                unique_indicators[indicator_type] = unique_to_attack
        
        return unique_indicators
    
    def _find_common_indicators(self, baseline_indicators: Dict[str, Set[str]], 
                               attack_indicators: Dict[str, Set[str]]) -> Dict[str, Set[str]]:
        """Find indicators that appear in both scenarios."""
        common_indicators = {}
        
        for indicator_type in baseline_indicators:
            baseline_set = baseline_indicators[indicator_type]
            attack_set = attack_indicators.get(indicator_type, set())
            
            # Find common indicators
            common = baseline_set & attack_set
            if common:
                common_indicators[indicator_type] = common
        
        return common_indicators
    
    def _detect_suspicious_patterns(self, attack_events: List[Event]) -> List[Dict[str, Any]]:
        """Detect suspicious patterns in attack events."""
        patterns = []
        
        # Group events by time windows
        time_windows = self._group_events_by_time_window(attack_events, window_minutes=5)
        
        for window_start, window_events in time_windows.items():
            if len(window_events) < 2:
                continue
            
            # Check for rapid succession of events
            if len(window_events) >= 5:
                patterns.append({
                    'type': 'rapid_activity',
                    'description': f'High activity burst: {len(window_events)} events in 5 minutes',
                    'timestamp': window_start.isoformat(),
                    'event_count': len(window_events),
                    'severity': 'high'
                })
            
            # Check for suspicious command sequences
            commands = []
            for event in window_events:
                if event.metadata and 'command' in event.metadata:
                    commands.append(event.metadata['command'].lower())
            
            if commands:
                suspicious_count = sum(1 for cmd in commands 
                                     if any(keyword in cmd for keyword in self.suspicious_keywords))
                
                if suspicious_count >= 2:
                    patterns.append({
                        'type': 'suspicious_commands',
                        'description': f'Multiple suspicious commands detected: {suspicious_count}/{len(commands)}',
                        'timestamp': window_start.isoformat(),
                        'commands': commands[:5],  # First 5 commands
                        'severity': 'high'
                    })
            
            # Check for privilege escalation patterns
            escalation_indicators = ['sudo', 'runas', 'privilege', 'admin', 'root']
            escalation_events = [e for e in window_events 
                               if any(indicator in e.description.lower() 
                                     for indicator in escalation_indicators)]
            
            if len(escalation_events) >= 2:
                patterns.append({
                    'type': 'privilege_escalation',
                    'description': f'Potential privilege escalation sequence detected',
                    'timestamp': window_start.isoformat(),
                    'event_count': len(escalation_events),
                    'severity': 'high'
                })
        
        return patterns
    
    def _analyze_timeline_anomalies(self, baseline_events: List[Event], 
                                   attack_events: List[Event]) -> List[Dict[str, Any]]:
        """Analyze timeline for anomalies compared to baseline."""
        anomalies = []
        
        if not baseline_events:
            return anomalies
        
        # Analyze event frequency patterns
        baseline_hourly = self._get_hourly_event_counts(baseline_events)
        attack_hourly = self._get_hourly_event_counts(attack_events)
        
        # Find hours with significantly higher activity
        for hour, attack_count in attack_hourly.items():
            baseline_count = baseline_hourly.get(hour, 0)
            
            # If attack activity is 3x higher than baseline
            if baseline_count > 0 and attack_count > baseline_count * 3:
                anomalies.append({
                    'type': 'activity_spike',
                    'description': f'Unusual activity spike at hour {hour}',
                    'hour': hour,
                    'baseline_count': baseline_count,
                    'attack_count': attack_count,
                    'multiplier': round(attack_count / baseline_count, 2),
                    'severity': 'medium'
                })
            elif baseline_count == 0 and attack_count > 5:
                anomalies.append({
                    'type': 'new_activity_window',
                    'description': f'New activity detected at hour {hour}',
                    'hour': hour,
                    'attack_count': attack_count,
                    'severity': 'medium'
                })
        
        return anomalies
    
    def _compare_severity_distributions(self, baseline_events: List[Event], 
                                       attack_events: List[Event]) -> Dict[str, Dict[str, int]]:
        """Compare severity distributions between scenarios."""
        baseline_severity = Counter(e.severity for e in baseline_events if e.severity)
        attack_severity = Counter(e.severity for e in attack_events if e.severity)
        
        return {
            'baseline': dict(baseline_severity),
            'attack': dict(attack_severity),
            'changes': {
                severity: attack_severity.get(severity, 0) - baseline_severity.get(severity, 0)
                for severity in set(list(baseline_severity.keys()) + list(attack_severity.keys()))
            }
        }
    
    def _find_new_event_types(self, baseline_events: List[Event], 
                             attack_events: List[Event]) -> Set[str]:
        """Find event types that appear only in attack scenario."""
        baseline_types = {e.event_type.value for e in baseline_events}
        attack_types = {e.event_type.value for e in attack_events}
        
        return attack_types - baseline_types
    
    def _generate_comparison_summary(self, result: ComparisonResult) -> Dict[str, Any]:
        """Generate a summary of the comparison results."""
        total_unique_indicators = sum(len(indicators) for indicators in result.unique_attack_indicators.values())
        
        risk_score = self._calculate_risk_score(result)
        
        return {
            'total_baseline_events': len(result.baseline_events),
            'total_attack_events': len(result.attack_events),
            'event_increase_ratio': len(result.attack_events) / max(len(result.baseline_events), 1),
            'unique_attack_indicators_count': total_unique_indicators,
            'suspicious_patterns_count': len(result.suspicious_patterns),
            'timeline_anomalies_count': len(result.timeline_anomalies),
            'new_event_types_count': len(result.new_event_types),
            'risk_score': risk_score,
            'risk_level': self._get_risk_level(risk_score)
        }
    
    def _calculate_risk_score(self, result: ComparisonResult) -> float:
        """Calculate overall risk score based on comparison results."""
        score = 0.0
        
        # Unique indicators (0-30 points)
        unique_count = sum(len(indicators) for indicators in result.unique_attack_indicators.values())
        score += min(unique_count * 2, 30)
        
        # Suspicious patterns (0-25 points)
        high_severity_patterns = sum(1 for p in result.suspicious_patterns if p.get('severity') == 'high')
        score += min(high_severity_patterns * 5, 25)
        
        # Timeline anomalies (0-20 points)
        score += min(len(result.timeline_anomalies) * 3, 20)
        
        # New event types (0-15 points)
        score += min(len(result.new_event_types) * 3, 15)
        
        # Event volume increase (0-10 points)
        if result.baseline_events:
            ratio = len(result.attack_events) / len(result.baseline_events)
            if ratio > 2:
                score += min((ratio - 1) * 2, 10)
        
        return min(score, 100.0)
    
    def _get_risk_level(self, risk_score: float) -> str:
        """Convert risk score to risk level."""
        if risk_score >= 70:
            return 'CRITICAL'
        elif risk_score >= 50:
            return 'HIGH'
        elif risk_score >= 30:
            return 'MEDIUM'
        elif risk_score >= 10:
            return 'LOW'
        else:
            return 'MINIMAL'
    
    def _group_events_by_time_window(self, events: List[Event], 
                                    window_minutes: int = 5) -> Dict[datetime, List[Event]]:
        """Group events by time windows."""
        windows = defaultdict(list)
        
        for event in events:
            # Round down to nearest window
            window_start = event.timestamp.replace(
                minute=(event.timestamp.minute // window_minutes) * window_minutes,
                second=0,
                microsecond=0
            )
            windows[window_start].append(event)
        
        return dict(windows)
    
    def _get_hourly_event_counts(self, events: List[Event]) -> Dict[int, int]:
        """Get event counts by hour of day."""
        hourly_counts = defaultdict(int)
        
        for event in events:
            hour = event.timestamp.hour
            hourly_counts[hour] += 1
        
        return dict(hourly_counts)
    
    def _is_valid_ip(self, ip_str: str) -> bool:
        """Check if string is a valid IP address."""
        try:
            ipaddress.ip_address(ip_str)
            return True
        except ValueError:
            return False
    
    def _is_valid_domain(self, domain_str: str) -> bool:
        """Check if string is a valid domain name."""
        if not domain_str or len(domain_str) > 255:
            return False
        
        # Basic domain validation
        domain_pattern = re.compile(
            r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        )
        
        return bool(domain_pattern.match(domain_str))
