"""
Baseline filtering system to suppress common events and highlight anomalies.
"""

from typing import List, Dict, Any, Set, Tuple
from collections import defaultdict, Counter
import logging
from datetime import datetime, timedelta

from ..models.event import Event, EventCategory, EventSource, EventType

logger = logging.getLogger(__name__)

class BaselineFilter:
    """
    Filters events to suppress common baseline activities and highlight
    anomalies and attack-specific events.
    """
    
    def __init__(self):
        # Common baseline processes that are usually benign
        self.common_processes = {
            'systemd', 'kernel', 'cron', 'sshd', 'apache2', 'nginx',
            'mysql', 'postgres', 'rsyslog', 'dbus', 'networkmanager',
            'systemd-resolved', 'systemd-timesyncd', 'systemd-logind'
        }
        
        # Common baseline network activities
        self.common_network_activities = {
            'dns_query', 'ntp_sync', 'dhcp_request', 'arp_request',
            'icmp_ping', 'ssh_keepalive'
        }
        
        # Suspicious indicators that should never be filtered
        self.never_filter_keywords = {
            'failed', 'error', 'denied', 'blocked', 'suspicious',
            'malware', 'virus', 'trojan', 'backdoor', 'exploit',
            'attack', 'intrusion', 'breach', 'compromise', 'unauthorized'
        }
        
        # Time-based filtering thresholds
        self.frequency_thresholds = {
            'high_frequency_limit': 100,  # Events per hour
            'burst_detection_window': 300,  # 5 minutes in seconds
            'burst_threshold': 20  # Events in burst window
        }
    
    def filter_events(self, baseline_events: List[Event], 
                     attack_events: List[Event]) -> Tuple[List[Event], List[Event]]:
        """
        Filter events to suppress common baseline activities.
        
        Args:
            baseline_events: Events from baseline scenario
            attack_events: Events from attack scenario
            
        Returns:
            Tuple of (filtered_baseline_events, filtered_attack_events)
        """
        logger.info(f"Filtering {len(baseline_events)} baseline and {len(attack_events)} attack events")
        
        # Learn baseline patterns
        baseline_patterns = self._learn_baseline_patterns(baseline_events)
        
        # Filter baseline events
        filtered_baseline = self._apply_baseline_filtering(baseline_events, baseline_patterns)
        
        # Filter attack events (more conservative)
        filtered_attack = self._apply_attack_filtering(attack_events, baseline_patterns)
        
        logger.info(f"Filtered to {len(filtered_baseline)} baseline and {len(filtered_attack)} attack events")
        
        return filtered_baseline, filtered_attack
    
    def _learn_baseline_patterns(self, baseline_events: List[Event]) -> Dict[str, Any]:
        """Learn patterns from baseline events for filtering."""
        patterns = {
            'common_processes': Counter(),
            'common_users': Counter(),
            'common_commands': Counter(),
            'common_destinations': Counter(),
            'hourly_patterns': defaultdict(list),
            'event_type_frequency': Counter(),
            'source_frequency': Counter()
        }
        
        for event in baseline_events:
            # Track event types and sources
            patterns['event_type_frequency'][event.event_type.value] += 1
            patterns['source_frequency'][event.source.value] += 1
            
            # Track hourly patterns
            hour = event.timestamp.hour
            patterns['hourly_patterns'][hour].append(event.event_type.value)
            
            # Extract patterns from metadata
            if event.metadata:
                # Process patterns
                for proc_field in ['process_name', 'proc_name', 'executable']:
                    if proc_field in event.metadata:
                        process = event.metadata[proc_field].split('/')[-1].split('\\')[-1].lower()
                        patterns['common_processes'][process] += 1
                
                # User patterns
                for user_field in ['username', 'user', 'account']:
                    if user_field in event.metadata:
                        user = event.metadata[user_field].lower()
                        patterns['common_users'][user] += 1
                
                # Command patterns
                if 'command' in event.metadata:
                    command = event.metadata['command'].lower()
                    # Extract base command (first word)
                    base_command = command.split()[0] if command.split() else command
                    patterns['common_commands'][base_command] += 1
                
                # Network destination patterns
                for dest_field in ['dst_ip', 'destination_ip', 'domain']:
                    if dest_field in event.metadata:
                        dest = event.metadata[dest_field].lower()
                        patterns['common_destinations'][dest] += 1
        
        # Calculate thresholds for "common" items (appear in >10% of events)
        total_events = len(baseline_events)
        threshold = max(total_events * 0.1, 5)  # At least 10% or 5 occurrences
        
        patterns['common_threshold'] = threshold
        
        return patterns
    
    def _apply_baseline_filtering(self, events: List[Event], 
                                 patterns: Dict[str, Any]) -> List[Event]:
        """Apply filtering to baseline events."""
        filtered_events = []
        
        for event in events:
            if self._should_keep_baseline_event(event, patterns):
                filtered_events.append(event)
        
        return filtered_events
    
    def _apply_attack_filtering(self, events: List[Event], 
                               patterns: Dict[str, Any]) -> List[Event]:
        """Apply filtering to attack events (more conservative)."""
        filtered_events = []
        
        for event in events:
            if self._should_keep_attack_event(event, patterns):
                filtered_events.append(event)
        
        return filtered_events
    
    def _should_keep_baseline_event(self, event: Event, patterns: Dict[str, Any]) -> bool:
        """Determine if a baseline event should be kept."""
        # Always keep high severity events
        if event.severity and event.severity.lower() in ['high', 'critical']:
            return True
        
        # Always keep events with suspicious keywords
        if self._contains_suspicious_keywords(event):
            return True
        
        # Filter out very common processes
        if event.metadata:
            for proc_field in ['process_name', 'proc_name', 'executable']:
                if proc_field in event.metadata:
                    process = event.metadata[proc_field].split('/')[-1].split('\\')[-1].lower()
                    if (process in patterns['common_processes'] and 
                        patterns['common_processes'][process] > patterns['common_threshold']):
                        return False
        
        # Filter out very common event types during normal hours
        hour = event.timestamp.hour
        if (8 <= hour <= 17 and  # Business hours
            event.event_type.value in patterns['event_type_frequency'] and
            patterns['event_type_frequency'][event.event_type.value] > patterns['common_threshold'] * 2):
            return False
        
        return True
    
    def _should_keep_attack_event(self, event: Event, patterns: Dict[str, Any]) -> bool:
        """Determine if an attack event should be kept (more conservative filtering)."""
        # Always keep high and medium severity events
        if event.severity and event.severity.lower() in ['high', 'critical', 'medium']:
            return True
        
        # Always keep events with suspicious keywords
        if self._contains_suspicious_keywords(event):
            return True
        
        # Keep events that are uncommon compared to baseline
        if self._is_uncommon_in_baseline(event, patterns):
            return True
        
        # Filter only extremely common and benign events
        if event.metadata:
            for proc_field in ['process_name', 'proc_name', 'executable']:
                if proc_field in event.metadata:
                    process = event.metadata[proc_field].split('/')[-1].split('\\')[-1].lower()
                    if (process in self.common_processes and
                        process in patterns['common_processes'] and
                        patterns['common_processes'][process] > patterns['common_threshold'] * 3):
                        return False
        
        return True
    
    def _contains_suspicious_keywords(self, event: Event) -> bool:
        """Check if event contains suspicious keywords."""
        text_to_check = [
            event.description.lower(),
            event.event_type.value.lower()
        ]
        
        if event.metadata:
            for key, value in event.metadata.items():
                if isinstance(value, str):
                    text_to_check.append(value.lower())
        
        combined_text = ' '.join(text_to_check)
        
        return any(keyword in combined_text for keyword in self.never_filter_keywords)
    
    def _is_uncommon_in_baseline(self, event: Event, patterns: Dict[str, Any]) -> bool:
        """Check if event represents uncommon activity compared to baseline."""
        # Check if event type is uncommon
        if (event.event_type.value not in patterns['event_type_frequency'] or
            patterns['event_type_frequency'][event.event_type.value] < patterns['common_threshold'] / 2):
            return True
        
        # Check if source is uncommon
        if (event.source.value not in patterns['source_frequency'] or
            patterns['source_frequency'][event.source.value] < patterns['common_threshold'] / 2):
            return True
        
        # Check metadata for uncommon patterns
        if event.metadata:
            # Uncommon processes
            for proc_field in ['process_name', 'proc_name', 'executable']:
                if proc_field in event.metadata:
                    process = event.metadata[proc_field].split('/')[-1].split('\\')[-1].lower()
                    if (process not in patterns['common_processes'] or
                        patterns['common_processes'][process] < patterns['common_threshold'] / 2):
                        return True
            
            # Uncommon users
            for user_field in ['username', 'user', 'account']:
                if user_field in event.metadata:
                    user = event.metadata[user_field].lower()
                    if (user not in patterns['common_users'] or
                        patterns['common_users'][user] < patterns['common_threshold'] / 2):
                        return True
            
            # Uncommon destinations
            for dest_field in ['dst_ip', 'destination_ip', 'domain']:
                if dest_field in event.metadata:
                    dest = event.metadata[dest_field].lower()
                    if (dest not in patterns['common_destinations'] or
                        patterns['common_destinations'][dest] < patterns['common_threshold'] / 2):
                        return True
        
        return False
    
    def create_filtered_timeline(self, baseline_events: List[Event], 
                                attack_events: List[Event]) -> Dict[str, Any]:
        """Create a filtered timeline highlighting important events."""
        filtered_baseline, filtered_attack = self.filter_events(baseline_events, attack_events)
        
        # Combine and sort by timestamp
        all_filtered_events = filtered_baseline + filtered_attack
        all_filtered_events.sort(key=lambda e: e.timestamp)
        
        # Calculate filtering statistics
        baseline_reduction = (len(baseline_events) - len(filtered_baseline)) / max(len(baseline_events), 1)
        attack_reduction = (len(attack_events) - len(filtered_attack)) / max(len(attack_events), 1)
        
        return {
            'filtered_events': all_filtered_events,
            'statistics': {
                'original_baseline_count': len(baseline_events),
                'filtered_baseline_count': len(filtered_baseline),
                'baseline_reduction_percentage': round(baseline_reduction * 100, 2),
                'original_attack_count': len(attack_events),
                'filtered_attack_count': len(filtered_attack),
                'attack_reduction_percentage': round(attack_reduction * 100, 2),
                'total_filtered_count': len(all_filtered_events),
                'total_reduction_percentage': round(
                    ((len(baseline_events) + len(attack_events)) - len(all_filtered_events)) /
                    max(len(baseline_events) + len(attack_events), 1) * 100, 2
                )
            }
        }
    
    def get_filtering_summary(self, baseline_events: List[Event], 
                             attack_events: List[Event]) -> Dict[str, Any]:
        """Get a summary of what would be filtered."""
        patterns = self._learn_baseline_patterns(baseline_events)
        
        # Count events that would be filtered
        baseline_filtered_count = sum(
            1 for event in baseline_events 
            if not self._should_keep_baseline_event(event, patterns)
        )
        
        attack_filtered_count = sum(
            1 for event in attack_events 
            if not self._should_keep_attack_event(event, patterns)
        )
        
        return {
            'baseline_events': len(baseline_events),
            'baseline_would_filter': baseline_filtered_count,
            'baseline_would_keep': len(baseline_events) - baseline_filtered_count,
            'attack_events': len(attack_events),
            'attack_would_filter': attack_filtered_count,
            'attack_would_keep': len(attack_events) - attack_filtered_count,
            'common_patterns_learned': {
                'processes': len(patterns['common_processes']),
                'users': len(patterns['common_users']),
                'commands': len(patterns['common_commands']),
                'destinations': len(patterns['common_destinations'])
            }
        }
