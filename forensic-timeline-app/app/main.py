"""
Main application entry point and web interface routes.
"""

from flask import Blueprint, render_template, request, jsonify, current_app
from werkzeug.utils import secure_filename
import os
import uuid
from datetime import datetime

bp = Blueprint('main', __name__)

@bp.route('/')
def index():
    """Main application interface."""
    return render_template('index.html')

@bp.route('/upload', methods=['GET', 'POST'])
def upload_files():
    """Handle file uploads for analysis."""
    if request.method == 'GET':
        return render_template('upload.html')

    # Handle POST request for file upload
    session_id = str(uuid.uuid4())
    upload_path = os.path.join(current_app.config['UPLOAD_FOLDER'], session_id)
    os.makedirs(upload_path, exist_ok=True)

    uploaded_files = {}

    # Process uploaded files
    for file_type in ['baseline_pcap', 'attack_pcap', 'baseline_logs', 'attack_logs']:
        if file_type in request.files:
            files = request.files.getlist(file_type)
            uploaded_files[file_type] = []

            for file in files:
                if file and file.filename:
                    filename = secure_filename(file.filename)
                    filepath = os.path.join(upload_path, f"{file_type}_{filename}")
                    file.save(filepath)
                    uploaded_files[file_type].append({
                        'filename': filename,
                        'filepath': filepath,
                        'size': os.path.getsize(filepath)
                    })

    # Store session info (in production, use database)
    session_info = {
        'session_id': session_id,
        'upload_time': datetime.utcnow().isoformat(),
        'files': uploaded_files,
        'status': 'uploaded'
    }

    return jsonify({
        'success': True,
        'session_id': session_id,
        'message': 'Files uploaded successfully',
        'files': uploaded_files
    })

@bp.route('/analyze/<session_id>')
def analyze(session_id):
    """Start analysis for uploaded files."""
    # TODO: Implement analysis orchestration
    return jsonify({
        'success': True,
        'session_id': session_id,
        'status': 'analysis_started',
        'message': 'Analysis started - this will be implemented in Phase 2'
    })

@bp.route('/timeline/<session_id>')
def timeline(session_id):
    """Display timeline visualization."""
    return render_template('timeline.html', session_id=session_id)

@bp.route('/report/<session_id>')
def report(session_id):
    """Generate and display analysis report."""
    return render_template('report.html', session_id=session_id)

@bp.route('/api/status/<session_id>')
def get_status(session_id):
    """Get analysis status for a session."""
    # TODO: Implement status tracking
    return jsonify({
        'session_id': session_id,
        'status': 'pending',
        'progress': 0,
        'message': 'Status tracking will be implemented in Phase 4'
    })

def allowed_file(filename, file_type):
    """Check if uploaded file has allowed extension."""
    if '.' not in filename:
        return False

    ext = '.' + filename.rsplit('.', 1)[1].lower()
    allowed_exts = current_app.config['ALLOWED_EXTENSIONS'].get(file_type, [])
    return ext in allowed_exts