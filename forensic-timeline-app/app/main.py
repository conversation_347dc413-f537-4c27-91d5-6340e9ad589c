"""
Main application entry point and web interface routes.
"""

from flask import Blueprint, render_template, request, jsonify, current_app, flash, redirect, url_for
from werkzeug.utils import secure_filename
import os
import uuid
import json
from datetime import datetime

from .models.event import EventCategory

bp = Blueprint('main', __name__)

# In-memory session storage (in production, use Redis or database)
analysis_sessions = {}

@bp.route('/')
def index():
    """Main application interface."""
    return render_template('index.html')

def allowed_file(filename):
    """Check if file extension is allowed."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

@bp.route('/upload', methods=['GET', 'POST'])
def upload_files():
    """Handle file uploads for analysis."""
    if request.method == 'GET':
        return render_template('upload.html')

    try:
        # Create new analysis session
        session_id = str(uuid.uuid4())
        upload_path = os.path.join(current_app.config['UPLOAD_FOLDER'], session_id)
        os.makedirs(upload_path, exist_ok=True)

        uploaded_files = []
        session_info = {
            'session_id': session_id,
            'created_at': datetime.utcnow(),
            'status': 'uploading',
            'uploaded_files': [],
            'total_events': 0,
            'progress': 0,
            'message': 'Files uploaded successfully'
        }

        # Process uploaded files
        for file_key in request.files:
            file = request.files[file_key]
            if file and file.filename and allowed_file(file.filename):
                filename = secure_filename(file.filename)

                # Determine file type and category from form data or filename
                file_type = request.form.get(f'{file_key}_type', 'logs')
                category_str = request.form.get(f'{file_key}_category', 'baseline')
                category = EventCategory.BASELINE if category_str == 'baseline' else EventCategory.ATTACK

                # Save file
                filepath = os.path.join(upload_path, f"{category.value}_{filename}")
                file.save(filepath)

                file_info = {
                    'filename': filename,
                    'filepath': filepath,
                    'file_type': file_type,
                    'category': category.value,
                    'size_bytes': os.path.getsize(filepath),
                    'upload_time': datetime.utcnow().isoformat()
                }

                session_info['uploaded_files'].append(file_info)
                uploaded_files.append(file_info)

        if not uploaded_files:
            return jsonify({'error': 'No valid files uploaded'}), 400

        # Store session
        session_info['status'] = 'uploaded'
        analysis_sessions[session_id] = session_info

        return jsonify({
            'success': True,
            'session_id': session_id,
            'uploaded_files': uploaded_files,
            'message': f'Uploaded {len(uploaded_files)} files successfully'
        })

    except Exception as e:
        current_app.logger.error(f"File upload failed: {e}")
        return jsonify({'error': str(e)}), 500

@bp.route('/analysis/<session_id>')
def analysis_page(session_id):
    """Analysis results page."""
    if session_id not in analysis_sessions:
        flash('Analysis session not found', 'error')
        return redirect(url_for('main.index'))

    session_data = analysis_sessions[session_id]
    return render_template('analysis.html',
                         session=session_data,
                         session_id=session_id)

@bp.route('/api/analyze/<session_id>', methods=['POST'])
def start_analysis(session_id):
    """Start analysis for uploaded files."""
    if session_id not in analysis_sessions:
        return jsonify({'error': 'Session not found'}), 404

    try:
        session_data = analysis_sessions[session_id]
        session_data['status'] = 'processing'
        session_data['progress'] = 0
        session_data['message'] = "Starting analysis..."

        # In a real implementation, this would trigger background processing
        # For now, we'll simulate the analysis

        return jsonify({
            'success': True,
            'session_id': session_id,
            'status': 'started',
            'message': 'Analysis started successfully'
        })

    except Exception as e:
        current_app.logger.error(f"Analysis start failed: {e}")
        return jsonify({'error': str(e)}), 500

@bp.route('/timeline/<session_id>')
def timeline_page(session_id):
    """Interactive timeline visualization page."""
    if session_id not in analysis_sessions:
        flash('Analysis session not found', 'error')
        return redirect(url_for('main.index'))

    session_data = analysis_sessions[session_id]
    return render_template('timeline.html',
                         session=session_data,
                         session_id=session_id)

@bp.route('/report/<session_id>')
def report_page(session_id):
    """Generate and display analysis report."""
    if session_id not in analysis_sessions:
        flash('Analysis session not found', 'error')
        return redirect(url_for('main.index'))

    session_data = analysis_sessions[session_id]
    return render_template('report.html',
                         session=session_data,
                         session_id=session_id)

@bp.route('/api/session/<session_id>')
def get_session(session_id):
    """Get session information."""
    if session_id not in analysis_sessions:
        return jsonify({'error': 'Session not found'}), 404

    session_data = analysis_sessions[session_id]
    # Convert datetime objects to ISO format for JSON serialization
    session_copy = session_data.copy()
    if 'created_at' in session_copy:
        session_copy['created_at'] = session_copy['created_at'].isoformat()

    return jsonify(session_copy)

@bp.route('/api/sessions')
def list_sessions():
    """List all analysis sessions."""
    sessions = []
    for session_id, session_data in analysis_sessions.items():
        sessions.append({
            'session_id': session_id,
            'created_at': session_data['created_at'].isoformat() if 'created_at' in session_data else None,
            'status': session_data.get('status', 'unknown'),
            'file_count': len(session_data.get('uploaded_files', [])),
            'total_events': session_data.get('total_events', 0)
        })

    return jsonify({'sessions': sessions})

@bp.route('/api/timeline/<session_id>')
def get_timeline_data(session_id):
    """Get timeline data for visualization."""
    if session_id not in analysis_sessions:
        return jsonify({'error': 'Session not found'}), 404

    # TODO: Return actual timeline data from analysis
    # For now, return sample data
    sample_events = [
        {
            'id': 1,
            'content': 'SSH Login Attempt',
            'start': '2023-01-15T14:30:00Z',
            'group': 'authentication',
            'className': 'event-high'
        },
        {
            'id': 2,
            'content': 'Process Creation',
            'start': '2023-01-15T14:31:00Z',
            'group': 'process',
            'className': 'event-medium'
        }
    ]

    return jsonify({
        'events': sample_events,
        'groups': [
            {'id': 'authentication', 'content': 'Authentication'},
            {'id': 'process', 'content': 'Process Activity'},
            {'id': 'network', 'content': 'Network Activity'}
        ]
    })