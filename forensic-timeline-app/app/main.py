"""
Main application entry point and web interface routes.
"""

from flask import Blueprint, render_template, request, jsonify, current_app, flash, redirect, url_for
from werkzeug.utils import secure_filename
import os
import uuid
import json
from datetime import datetime

from .models.event import EventCategory
from .analyzers.network_analyzer import NetworkAnalyzer
from .parsers.log_normalizer import LogNormalizer
from .config import Config

bp = Blueprint('main', __name__)

# In-memory session storage (in production, use Redis or database)
analysis_sessions = {}

@bp.route('/')
def index():
    """Main application interface."""
    return render_template('index.html')

def allowed_file(filename):
    """Check if file extension is allowed."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

@bp.route('/upload', methods=['GET', 'POST'])
def upload_files():
    """Handle file uploads for analysis."""
    if request.method == 'GET':
        return render_template('upload.html')

    try:
        # Create new analysis session
        session_id = str(uuid.uuid4())
        upload_path = os.path.join(current_app.config['UPLOAD_FOLDER'], session_id)
        os.makedirs(upload_path, exist_ok=True)

        uploaded_files = []
        session_info = {
            'session_id': session_id,
            'created_at': datetime.utcnow(),
            'status': 'uploading',
            'uploaded_files': [],
            'total_events': 0,
            'progress': 0,
            'message': 'Files uploaded successfully'
        }

        # Process uploaded files
        for file_key in request.files:
            file = request.files[file_key]
            if file and file.filename and allowed_file(file.filename):
                filename = secure_filename(file.filename)

                # Determine file type and category from form data or filename
                file_type = request.form.get(f'{file_key}_type')
                if not file_type:
                    # Auto-detect file type from extension
                    if filename.lower().endswith(('.pcap', '.pcapng')):
                        file_type = 'pcap'
                    else:
                        file_type = 'logs'

                category_str = request.form.get(f'{file_key}_category')
                if not category_str:
                    # Auto-detect category from file key
                    if 'baseline' in file_key.lower():
                        category_str = 'baseline'
                    elif 'attack' in file_key.lower():
                        category_str = 'attack'
                    else:
                        category_str = 'baseline'  # default
                category = EventCategory.BASELINE if category_str == 'baseline' else EventCategory.ATTACK

                # Save file
                filepath = os.path.join(upload_path, f"{category.value}_{filename}")
                file.save(filepath)

                file_info = {
                    'filename': filename,
                    'filepath': filepath,
                    'file_type': file_type,
                    'category': category.value,
                    'size_bytes': os.path.getsize(filepath),
                    'upload_time': datetime.utcnow().isoformat()
                }

                session_info['uploaded_files'].append(file_info)
                uploaded_files.append(file_info)

        if not uploaded_files:
            return jsonify({'error': 'No valid files uploaded'}), 400

        # Store session
        session_info['status'] = 'uploaded'
        analysis_sessions[session_id] = session_info

        return jsonify({
            'success': True,
            'session_id': session_id,
            'uploaded_files': uploaded_files,
            'message': f'Uploaded {len(uploaded_files)} files successfully'
        })

    except Exception as e:
        current_app.logger.error(f"File upload failed: {e}")
        return jsonify({'error': str(e)}), 500

@bp.route('/analysis/<session_id>')
def analysis_page(session_id):
    """Analysis results page."""
    if session_id not in analysis_sessions:
        flash('Analysis session not found', 'error')
        return redirect(url_for('main.index'))

    session_data = analysis_sessions[session_id]
    return render_template('analysis.html',
                         session=session_data,
                         session_id=session_id)

@bp.route('/api/analyze/<session_id>', methods=['POST'])
def start_analysis(session_id):
    """Start analysis for uploaded files."""
    if session_id not in analysis_sessions:
        return jsonify({'error': 'Session not found'}), 404

    try:
        session_data = analysis_sessions[session_id]
        session_data['status'] = 'processing'
        session_data['progress'] = 0
        session_data['message'] = "Starting analysis..."

        # Initialize analyzers
        config = Config()
        network_analyzer = NetworkAnalyzer(config)
        log_normalizer = LogNormalizer()

        # Process uploaded files
        upload_path = os.path.join(current_app.config['UPLOAD_FOLDER'], session_id)
        all_events = []

        # Separate files by type
        pcap_files = []
        log_files = []

        for file_info in session_data['uploaded_files']:
            filepath = file_info['filepath']
            file_type = file_info.get('file_type', 'logs')

            if file_type == 'pcap' or filepath.lower().endswith(('.pcap', '.pcapng')):
                pcap_files.append(file_info)
            else:
                log_files.append(file_info)

        # Process PCAP files
        for file_info in pcap_files:
            filepath = file_info['filepath']
            category = EventCategory.BASELINE if file_info['category'] == 'baseline' else EventCategory.ATTACK

            current_app.logger.info(f"Analyzing PCAP file: {filepath}")
            session_data['message'] = f"Analyzing network traffic: {file_info['filename']}"

            try:
                # Run network analysis
                events = network_analyzer.analyze_pcap(filepath, category, upload_path)
                all_events.extend(events)
                current_app.logger.info(f"Generated {len(events)} network events from {file_info['filename']}")
            except Exception as e:
                current_app.logger.error(f"PCAP analysis failed for {filepath}: {e}")
                session_data['message'] = f"Network analysis failed for {file_info['filename']}: {str(e)}"

        # Process log files
        for file_info in log_files:
            filepath = file_info['filepath']
            category = EventCategory.BASELINE if file_info['category'] == 'baseline' else EventCategory.ATTACK

            current_app.logger.info(f"Analyzing log file: {filepath}")
            session_data['message'] = f"Analyzing system logs: {file_info['filename']}"

            try:
                # Run log analysis
                events = log_normalizer.parse_single_log_file(filepath, category)
                all_events.extend(events)
                current_app.logger.info(f"Generated {len(events)} log events from {file_info['filename']}")
            except Exception as e:
                current_app.logger.error(f"Log analysis failed for {filepath}: {e}")
                session_data['message'] = f"Log analysis failed for {file_info['filename']}: {str(e)}"

        # Update session with results
        session_data['status'] = 'completed'
        session_data['progress'] = 100
        session_data['total_events'] = len(all_events)

        # Create detailed completion message
        pcap_count = len(pcap_files)
        log_count = len(log_files)
        analysis_summary = []
        if pcap_count > 0:
            analysis_summary.append(f"{pcap_count} PCAP file{'s' if pcap_count != 1 else ''}")
        if log_count > 0:
            analysis_summary.append(f"{log_count} log file{'s' if log_count != 1 else ''}")

        files_analyzed = " and ".join(analysis_summary) if analysis_summary else "no files"
        session_data['message'] = f"Analysis completed. Processed {files_analyzed}, generated {len(all_events)} timeline events."
        session_data['events'] = [event.to_dict() for event in all_events]

        return jsonify({
            'success': True,
            'session_id': session_id,
            'status': 'completed',
            'total_events': len(all_events),
            'pcap_files': pcap_count,
            'log_files': log_count,
            'message': f'Analysis completed successfully. Processed {files_analyzed}, generated {len(all_events)} timeline events.'
        })

    except Exception as e:
        current_app.logger.error(f"Analysis failed: {e}")
        session_data['status'] = 'failed'
        session_data['message'] = f"Analysis failed: {str(e)}"
        return jsonify({'error': str(e)}), 500

@bp.route('/timeline/<session_id>')
def timeline_page(session_id):
    """Interactive timeline visualization page."""
    if session_id not in analysis_sessions:
        flash('Analysis session not found', 'error')
        return redirect(url_for('main.index'))

    session_data = analysis_sessions[session_id]
    return render_template('timeline.html',
                         session=session_data,
                         session_id=session_id)

@bp.route('/report/<session_id>')
def report_page(session_id):
    """Generate and display analysis report."""
    if session_id not in analysis_sessions:
        flash('Analysis session not found', 'error')
        return redirect(url_for('main.index'))

    session_data = analysis_sessions[session_id]
    return render_template('report.html',
                         session=session_data,
                         session_id=session_id)

@bp.route('/api/session/<session_id>')
def get_session(session_id):
    """Get session information."""
    if session_id not in analysis_sessions:
        return jsonify({'error': 'Session not found'}), 404

    session_data = analysis_sessions[session_id]
    # Convert datetime objects to ISO format for JSON serialization
    session_copy = session_data.copy()
    if 'created_at' in session_copy:
        session_copy['created_at'] = session_copy['created_at'].isoformat()

    return jsonify(session_copy)

@bp.route('/api/sessions')
def list_sessions():
    """List all analysis sessions."""
    sessions = []
    for session_id, session_data in analysis_sessions.items():
        sessions.append({
            'session_id': session_id,
            'created_at': session_data['created_at'].isoformat() if 'created_at' in session_data else None,
            'status': session_data.get('status', 'unknown'),
            'file_count': len(session_data.get('uploaded_files', [])),
            'total_events': session_data.get('total_events', 0)
        })

    return jsonify({'sessions': sessions})

@bp.route('/api/timeline/<session_id>')
def get_timeline_data(session_id):
    """Get timeline data for visualization."""
    if session_id not in analysis_sessions:
        return jsonify({'error': 'Session not found'}), 404

    session_data = analysis_sessions[session_id]

    # Check if we have events from analysis
    events_data = session_data.get('events', [])

    if events_data:
        try:
            # Convert events to vis.js format
            vis_events = []
            groups_set = set()

            for i, event in enumerate(events_data):
                # Determine group based on event source or type
                group = event.get('source', 'unknown')
                groups_set.add(group)

                # Determine severity class
                severity = event.get('severity', 'low')
                class_name = f'event-{severity}'

                vis_event = {
                    'id': i + 1,
                    'content': event.get('description', 'Unknown Event'),
                    'start': event.get('timestamp', '2023-01-01T00:00:00Z'),
                    'group': group,
                    'className': class_name,
                    'title': f"Source: {event.get('source', 'Unknown')}<br>Type: {event.get('event_type', 'Unknown')}<br>Severity: {severity}"
                }
                vis_events.append(vis_event)

            # Create groups
            vis_groups = [{'id': group, 'content': group.title()} for group in sorted(groups_set)]

            return jsonify({
                'events': vis_events,
                'groups': vis_groups
            })

        except Exception as e:
            current_app.logger.error(f"Error processing timeline data: {e}")
            # Fall back to sample data
            pass

    # Return sample data if no analysis results available
    sample_events = [
        {
            'id': 1,
            'content': 'SSH Login Attempt',
            'start': '2023-01-15T14:30:00Z',
            'group': 'authentication',
            'className': 'event-high',
            'title': 'Source: System Log<br>Type: Authentication<br>Severity: high'
        },
        {
            'id': 2,
            'content': 'Process Creation',
            'start': '2023-01-15T14:31:00Z',
            'group': 'process',
            'className': 'event-medium',
            'title': 'Source: Process Monitor<br>Type: Process<br>Severity: medium'
        },
        {
            'id': 3,
            'content': 'Network Connection',
            'start': '2023-01-15T14:32:00Z',
            'group': 'network',
            'className': 'event-low',
            'title': 'Source: Network Monitor<br>Type: Network<br>Severity: low'
        }
    ]

    return jsonify({
        'events': sample_events,
        'groups': [
            {'id': 'authentication', 'content': 'Authentication'},
            {'id': 'process', 'content': 'Process Activity'},
            {'id': 'network', 'content': 'Network Activity'}
        ]
    })

@bp.route('/api/report/<session_id>', methods=['POST'])
def generate_report_api(session_id):
    """API endpoint to generate analysis reports."""
    if session_id not in analysis_sessions:
        return jsonify({'error': 'Session not found'}), 404

    try:
        data = request.get_json()
        format_type = data.get('format', 'html') if data else 'html'

        # Validate format
        if format_type not in ['html', 'json', 'markdown']:
            return jsonify({'error': 'Invalid format. Supported: html, json, markdown'}), 400

        session_data = analysis_sessions[session_id]

        # Create reports directory
        reports_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], session_id, 'reports')
        os.makedirs(reports_dir, exist_ok=True)

        # Generate report filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"forensic_report_{timestamp}.{format_type}"
        output_path = os.path.join(reports_dir, filename)

        # For now, generate a sample report since we don't have full analysis data
        report_content = generate_sample_report(session_data, format_type)

        # Write report to file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        # Calculate file size
        file_size = os.path.getsize(output_path)

        return jsonify({
            'success': True,
            'format': format_type,
            'filename': filename,
            'size': f"{file_size:,} bytes",
            'download_url': f'/download/report/{session_id}/{filename}',
            'preview_url': f'/preview/report/{session_id}/{filename}' if format_type == 'html' else None,
            'summary': {
                'total_events': len(session_data.get('uploaded_files', [])),
                'correlations': 'Sample data',
                'threat_level': 'LOW'
            }
        })

    except Exception as e:
        current_app.logger.error(f"Report generation failed: {e}")
        return jsonify({'error': str(e)}), 500

def generate_sample_report(session_data, format_type):
    """Generate a sample report for demonstration."""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    if format_type == 'html':
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>Forensic Analysis Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .header {{ background: #f8f9fa; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; }}
        .metric {{ background: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Forensic Timeline Analysis Report</h1>
        <p><strong>Generated:</strong> {timestamp}</p>
        <p><strong>Session ID:</strong> {session_data.get('session_id', 'Unknown')}</p>
    </div>

    <div class="section">
        <h2>📊 Executive Summary</h2>
        <div class="metric"><strong>Files Analyzed:</strong> {len(session_data.get('uploaded_files', []))}</div>
        <div class="metric"><strong>Threat Level:</strong> LOW</div>
        <div class="metric"><strong>Analysis Status:</strong> {session_data.get('status', 'Complete')}</div>
    </div>

    <div class="section">
        <h2>📁 Uploaded Files</h2>
        <ul>
        {''.join(f'<li>{file.get("filename", "Unknown")} ({file.get("size_bytes", 0):,} bytes)</li>'
                for file in session_data.get('uploaded_files', []))}
        </ul>
    </div>

    <div class="section">
        <h2>🎯 Key Findings</h2>
        <ul>
            <li>Analysis completed successfully</li>
            <li>No critical threats detected in sample data</li>
            <li>Timeline visualization available</li>
            <li>Ready for detailed investigation</li>
        </ul>
    </div>

    <div class="section">
        <h2>📈 Recommendations</h2>
        <ul>
            <li>Review timeline for suspicious patterns</li>
            <li>Correlate events across data sources</li>
            <li>Investigate any anomalous activities</li>
            <li>Document findings for incident response</li>
        </ul>
    </div>

    <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6;">
        <p><em>Generated by Forensic Timeline Analysis v1.0</em></p>
    </footer>
</body>
</html>
        """
    elif format_type == 'json':
        return json.dumps({
            'report_metadata': {
                'generated_at': timestamp,
                'session_id': session_data.get('session_id', 'Unknown'),
                'version': '1.0'
            },
            'executive_summary': {
                'files_analyzed': len(session_data.get('uploaded_files', [])),
                'threat_level': 'LOW',
                'status': session_data.get('status', 'Complete')
            },
            'uploaded_files': session_data.get('uploaded_files', []),
            'key_findings': [
                'Analysis completed successfully',
                'No critical threats detected in sample data',
                'Timeline visualization available',
                'Ready for detailed investigation'
            ],
            'recommendations': [
                'Review timeline for suspicious patterns',
                'Correlate events across data sources',
                'Investigate any anomalous activities',
                'Document findings for incident response'
            ]
        }, indent=2)
    else:  # markdown
        return f"""# 🔍 Forensic Timeline Analysis Report

**Generated:** {timestamp}
**Session ID:** {session_data.get('session_id', 'Unknown')}

## 📊 Executive Summary

- **Files Analyzed:** {len(session_data.get('uploaded_files', []))}
- **Threat Level:** LOW
- **Analysis Status:** {session_data.get('status', 'Complete')}

## 📁 Uploaded Files

{chr(10).join(f'- {file.get("filename", "Unknown")} ({file.get("size_bytes", 0):,} bytes)'
              for file in session_data.get('uploaded_files', []))}

## 🎯 Key Findings

- Analysis completed successfully
- No critical threats detected in sample data
- Timeline visualization available
- Ready for detailed investigation

## 📈 Recommendations

- Review timeline for suspicious patterns
- Correlate events across data sources
- Investigate any anomalous activities
- Document findings for incident response

---
*Generated by Forensic Timeline Analysis v1.0*
        """

@bp.route('/download/report/<session_id>/<filename>')
def download_report(session_id, filename):
    """Download generated report file."""
    if session_id not in analysis_sessions:
        return jsonify({'error': 'Session not found'}), 404

    try:
        reports_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], session_id, 'reports')
        file_path = os.path.join(reports_dir, filename)

        if not os.path.exists(file_path):
            return jsonify({'error': 'Report file not found'}), 404

        from flask import send_file
        return send_file(file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        current_app.logger.error(f"Report download failed: {e}")
        return jsonify({'error': 'Download failed'}), 500

@bp.route('/preview/report/<session_id>/<filename>')
def preview_report(session_id, filename):
    """Preview HTML report in browser."""
    if session_id not in analysis_sessions:
        return jsonify({'error': 'Session not found'}), 404

    try:
        reports_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], session_id, 'reports')
        file_path = os.path.join(reports_dir, filename)

        if not os.path.exists(file_path):
            return jsonify({'error': 'Report file not found'}), 404

        # Only allow preview for HTML files
        if not filename.endswith('.html'):
            return jsonify({'error': 'Preview only available for HTML reports'}), 400

        from flask import send_file
        return send_file(file_path)

    except Exception as e:
        current_app.logger.error(f"Report preview failed: {e}")
        return jsonify({'error': 'Preview failed'}), 500