"""
Configuration settings for the Forensic Timeline Analysis application.
"""

import os
from datetime import timedelta

basedir = os.path.abspath(os.path.dirname(__file__))

class Config:
    """Base configuration class."""

    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'forensic-timeline-dev-key-change-in-production'

    # File upload settings
    MAX_CONTENT_LENGTH = 2 * 1024 * 1024 * 1024  # 2GB max file size
    UPLOAD_FOLDER = os.path.join(basedir, '..', 'uploads')
    ALLOWED_EXTENSIONS = {
        'pcap': ['.pcap', '.pcapng'],
        'logs': ['.evtx', '.log', '.txt', '.csv', '.json']
    }

    # Analysis settings
    ANALYSIS_TIMEOUT = 3600  # 1 hour timeout for analysis
    TEMP_DIR = os.path.join(basedir, '..', 'temp')
    RESULTS_DIR = os.path.join(basedir, '..', 'results')

    # Network analysis tools
    SURICATA_PATH = '/usr/bin/suricata'
    ZEEK_PATH = '/usr/local/zeek/bin/zeek'
    SURICATA_CONFIG = '/etc/suricata/suricata.yaml'
    SURICATA_RULES = '/var/lib/suricata/rules'

    # Database settings (SQLite for simplicity)
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, '..', 'forensic_timeline.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Session settings
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)

    # Logging
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'

    @staticmethod
    def init_app(app):
        """Initialize application with this config."""
        # Create necessary directories
        for directory in [Config.UPLOAD_FOLDER, Config.TEMP_DIR, Config.RESULTS_DIR]:
            os.makedirs(directory, exist_ok=True)

class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False
    LOG_LEVEL = 'WARNING'

class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}