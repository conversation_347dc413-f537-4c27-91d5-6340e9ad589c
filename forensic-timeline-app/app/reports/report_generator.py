"""
Report generation system for forensic timeline analysis.
"""

import os
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging

from ..models.event import Event
from ..models.timeline import Timeline
from ..models.correlation import CorrelationResult
from ..analyzers.comparison_engine import ComparisonResult

logger = logging.getLogger(__name__)

class ReportGenerator:
    """
    Generates comprehensive forensic analysis reports in multiple formats.
    """
    
    def __init__(self, template_dir: Optional[str] = None):
        self.template_dir = template_dir or os.path.join(
            os.path.dirname(__file__), 'templates'
        )
        
        # Ensure template directory exists
        os.makedirs(self.template_dir, exist_ok=True)
    
    def generate_comprehensive_report(self, 
                                    timeline: Timeline,
                                    correlations: List[CorrelationResult],
                                    comparison_result: Optional[ComparisonResult] = None,
                                    output_path: str = "forensic_report.html",
                                    format_type: str = "html") -> str:
        """
        Generate a comprehensive forensic analysis report.
        
        Args:
            timeline: Timeline with all events
            correlations: List of correlation results
            comparison_result: Optional baseline vs attack comparison
            output_path: Output file path
            format_type: Report format ('html', 'json', 'markdown')
            
        Returns:
            Path to generated report
        """
        logger.info(f"Generating {format_type} report: {output_path}")
        
        # Prepare report data
        report_data = self._prepare_report_data(
            timeline, correlations, comparison_result
        )
        
        # Generate report based on format
        if format_type.lower() == 'html':
            return self._generate_html_report(report_data, output_path)
        elif format_type.lower() == 'json':
            return self._generate_json_report(report_data, output_path)
        elif format_type.lower() == 'markdown':
            return self._generate_markdown_report(report_data, output_path)
        else:
            raise ValueError(f"Unsupported report format: {format_type}")
    
    def _prepare_report_data(self, 
                           timeline: Timeline,
                           correlations: List[CorrelationResult],
                           comparison_result: Optional[ComparisonResult]) -> Dict[str, Any]:
        """Prepare data for report generation."""
        events = timeline.events
        
        # Basic statistics
        baseline_events = [e for e in events if e.category.value == 'baseline']
        attack_events = [e for e in events if e.category.value == 'attack']
        
        # Time range analysis
        if events:
            start_time = min(e.timestamp for e in events)
            end_time = max(e.timestamp for e in events)
            duration = (end_time - start_time).total_seconds()
        else:
            start_time = end_time = None
            duration = 0
        
        # Event distribution analysis
        from collections import Counter
        source_distribution = Counter(e.source.value for e in events)
        type_distribution = Counter(e.event_type.value for e in events)
        severity_distribution = Counter(e.severity for e in events if e.severity)
        
        # Correlation analysis
        correlation_summary = self._analyze_correlations(correlations)
        
        # Timeline analysis
        timeline_analysis = self._analyze_timeline_patterns(events)
        
        # Top events by severity
        high_severity_events = [
            e for e in events 
            if e.severity and e.severity.lower() in ['high', 'critical']
        ]
        high_severity_events.sort(key=lambda e: e.timestamp)
        
        report_data = {
            'metadata': {
                'generated_at': datetime.utcnow().isoformat(),
                'session_id': timeline.session_id,
                'report_version': '1.0',
                'total_events': len(events),
                'analysis_duration_seconds': duration
            },
            'executive_summary': self._generate_executive_summary(
                events, correlations, comparison_result
            ),
            'timeline_overview': {
                'start_time': start_time.isoformat() if start_time else None,
                'end_time': end_time.isoformat() if end_time else None,
                'duration_seconds': duration,
                'total_events': len(events),
                'baseline_events': len(baseline_events),
                'attack_events': len(attack_events)
            },
            'event_analysis': {
                'source_distribution': dict(source_distribution),
                'type_distribution': dict(type_distribution),
                'severity_distribution': dict(severity_distribution),
                'timeline_patterns': timeline_analysis
            },
            'correlation_analysis': correlation_summary,
            'high_severity_events': [
                self._event_to_report_dict(e) for e in high_severity_events[:20]
            ],
            'comparison_analysis': comparison_result.to_dict() if comparison_result else None,
            'recommendations': self._generate_recommendations(
                events, correlations, comparison_result
            ),
            'technical_details': {
                'top_correlations': [
                    corr.to_dict() for corr in correlations[:10]
                ],
                'event_samples': [
                    self._event_to_report_dict(e) for e in events[:10]
                ]
            }
        }
        
        return report_data
    
    def _generate_executive_summary(self, 
                                   events: List[Event],
                                   correlations: List[CorrelationResult],
                                   comparison_result: Optional[ComparisonResult]) -> Dict[str, Any]:
        """Generate executive summary section."""
        baseline_events = [e for e in events if e.category.value == 'baseline']
        attack_events = [e for e in events if e.category.value == 'attack']
        
        # Key findings
        key_findings = []
        
        if attack_events:
            key_findings.append(f"Analyzed {len(attack_events)} attack-related events")
        
        if correlations:
            high_confidence_correlations = [
                c for c in correlations if c.score >= 0.7
            ]
            key_findings.append(
                f"Identified {len(high_confidence_correlations)} high-confidence event correlations"
            )
        
        if comparison_result:
            risk_level = comparison_result.comparison_summary.get('risk_level', 'UNKNOWN')
            key_findings.append(f"Overall risk assessment: {risk_level}")
            
            unique_indicators = sum(
                len(indicators) for indicators in comparison_result.unique_attack_indicators.values()
            )
            if unique_indicators > 0:
                key_findings.append(f"Found {unique_indicators} unique attack indicators")
        
        # Threat assessment
        threat_level = "LOW"
        if comparison_result:
            risk_score = comparison_result.comparison_summary.get('risk_score', 0)
            if risk_score >= 70:
                threat_level = "CRITICAL"
            elif risk_score >= 50:
                threat_level = "HIGH"
            elif risk_score >= 30:
                threat_level = "MEDIUM"
        
        return {
            'key_findings': key_findings,
            'threat_level': threat_level,
            'events_analyzed': len(events),
            'correlations_found': len(correlations),
            'analysis_confidence': self._calculate_analysis_confidence(events, correlations)
        }
    
    def _analyze_correlations(self, correlations: List[CorrelationResult]) -> Dict[str, Any]:
        """Analyze correlation patterns."""
        if not correlations:
            return {'total': 0, 'patterns': {}}
        
        # Group correlations by rules matched
        from collections import defaultdict
        rule_patterns = defaultdict(int)
        
        for corr in correlations:
            for rule in corr.rules_matched:
                rule_patterns[rule] += 1
        
        # Calculate statistics
        scores = [c.score for c in correlations]
        
        return {
            'total': len(correlations),
            'average_score': sum(scores) / len(scores),
            'max_score': max(scores),
            'min_score': min(scores),
            'high_confidence_count': len([c for c in correlations if c.score >= 0.7]),
            'rule_patterns': dict(rule_patterns),
            'top_correlations': [
                {
                    'score': c.score,
                    'rules': c.rules_matched,
                    'event1_desc': c.event1.description[:100],
                    'event2_desc': c.event2.description[:100]
                }
                for c in sorted(correlations, key=lambda x: x.score, reverse=True)[:5]
            ]
        }
    
    def _analyze_timeline_patterns(self, events: List[Event]) -> Dict[str, Any]:
        """Analyze timeline patterns."""
        if not events:
            return {}
        
        # Hourly distribution
        from collections import defaultdict
        hourly_counts = defaultdict(int)
        daily_counts = defaultdict(int)
        
        for event in events:
            hour = event.timestamp.hour
            day = event.timestamp.date().isoformat()
            hourly_counts[hour] += 1
            daily_counts[day] += 1
        
        # Find peak activity periods
        peak_hour = max(hourly_counts.items(), key=lambda x: x[1]) if hourly_counts else (0, 0)
        peak_day = max(daily_counts.items(), key=lambda x: x[1]) if daily_counts else ("", 0)
        
        return {
            'hourly_distribution': dict(hourly_counts),
            'daily_distribution': dict(daily_counts),
            'peak_hour': {'hour': peak_hour[0], 'count': peak_hour[1]},
            'peak_day': {'date': peak_day[0], 'count': peak_day[1]},
            'total_days_analyzed': len(daily_counts)
        }
    
    def _generate_recommendations(self, 
                                events: List[Event],
                                correlations: List[CorrelationResult],
                                comparison_result: Optional[ComparisonResult]) -> List[Dict[str, str]]:
        """Generate security recommendations based on analysis."""
        recommendations = []
        
        # High severity events recommendations
        high_severity_count = len([
            e for e in events 
            if e.severity and e.severity.lower() in ['high', 'critical']
        ])
        
        if high_severity_count > 0:
            recommendations.append({
                'category': 'Immediate Action',
                'priority': 'HIGH',
                'recommendation': f'Investigate {high_severity_count} high-severity security events immediately',
                'details': 'High-severity events may indicate active threats or successful attacks'
            })
        
        # Correlation-based recommendations
        if correlations:
            high_confidence_correlations = [c for c in correlations if c.score >= 0.7]
            if high_confidence_correlations:
                recommendations.append({
                    'category': 'Investigation',
                    'priority': 'MEDIUM',
                    'recommendation': f'Analyze {len(high_confidence_correlations)} high-confidence event correlations',
                    'details': 'These correlations may reveal attack patterns or coordinated activities'
                })
        
        # Comparison-based recommendations
        if comparison_result:
            risk_level = comparison_result.comparison_summary.get('risk_level', 'UNKNOWN')
            
            if risk_level in ['CRITICAL', 'HIGH']:
                recommendations.append({
                    'category': 'Risk Mitigation',
                    'priority': 'HIGH',
                    'recommendation': f'Address {risk_level} risk level findings immediately',
                    'details': 'Significant deviations from baseline detected'
                })
            
            # Unique indicators recommendations
            unique_indicators = comparison_result.unique_attack_indicators
            if 'ip_addresses' in unique_indicators and unique_indicators['ip_addresses']:
                recommendations.append({
                    'category': 'Network Security',
                    'priority': 'MEDIUM',
                    'recommendation': 'Block or monitor suspicious IP addresses',
                    'details': f'Found {len(unique_indicators["ip_addresses"])} unique IP addresses in attack scenario'
                })
        
        # General recommendations
        recommendations.extend([
            {
                'category': 'Monitoring',
                'priority': 'LOW',
                'recommendation': 'Enhance logging and monitoring capabilities',
                'details': 'Improve visibility into system and network activities'
            },
            {
                'category': 'Documentation',
                'priority': 'LOW',
                'recommendation': 'Document findings and update incident response procedures',
                'details': 'Ensure lessons learned are captured for future incidents'
            }
        ])
        
        return recommendations
    
    def _calculate_analysis_confidence(self, events: List[Event], 
                                     correlations: List[CorrelationResult]) -> str:
        """Calculate confidence level of the analysis."""
        confidence_score = 0
        
        # More events = higher confidence
        if len(events) >= 100:
            confidence_score += 30
        elif len(events) >= 50:
            confidence_score += 20
        elif len(events) >= 10:
            confidence_score += 10
        
        # Correlations increase confidence
        if correlations:
            high_conf_correlations = len([c for c in correlations if c.score >= 0.7])
            confidence_score += min(high_conf_correlations * 5, 30)
        
        # Multiple sources increase confidence
        sources = set(e.source.value for e in events)
        confidence_score += min(len(sources) * 10, 40)
        
        if confidence_score >= 80:
            return "HIGH"
        elif confidence_score >= 60:
            return "MEDIUM"
        elif confidence_score >= 40:
            return "LOW"
        else:
            return "VERY LOW"
    
    def _event_to_report_dict(self, event: Event) -> Dict[str, Any]:
        """Convert event to dictionary for report."""
        return {
            'timestamp': event.timestamp.isoformat(),
            'source': event.source.value,
            'event_type': event.event_type.value,
            'category': event.category.value,
            'severity': event.severity,
            'description': event.description,
            'metadata': event.metadata or {}
        }
    
    def _generate_html_report(self, report_data: Dict[str, Any], output_path: str) -> str:
        """Generate HTML report."""
        html_template = self._get_html_template()
        
        # Simple template substitution (in production, use a proper template engine)
        html_content = html_template.format(**{
            'title': f"Forensic Analysis Report - {report_data['metadata']['session_id']}",
            'generated_at': report_data['metadata']['generated_at'],
            'session_id': report_data['metadata']['session_id'],
            'total_events': report_data['metadata']['total_events'],
            'threat_level': report_data['executive_summary']['threat_level'],
            'key_findings': '<br>'.join(f"• {finding}" for finding in report_data['executive_summary']['key_findings']),
            'correlations_found': report_data['executive_summary']['correlations_found'],
            'analysis_confidence': report_data['executive_summary']['analysis_confidence'],
            'report_data_json': json.dumps(report_data, indent=2, default=str)
        })
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML report generated: {output_path}")
        return output_path
    
    def _generate_json_report(self, report_data: Dict[str, Any], output_path: str) -> str:
        """Generate JSON report."""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        logger.info(f"JSON report generated: {output_path}")
        return output_path
    
    def _generate_markdown_report(self, report_data: Dict[str, Any], output_path: str) -> str:
        """Generate Markdown report."""
        markdown_content = self._get_markdown_template().format(**{
            'session_id': report_data['metadata']['session_id'],
            'generated_at': report_data['metadata']['generated_at'],
            'total_events': report_data['metadata']['total_events'],
            'threat_level': report_data['executive_summary']['threat_level'],
            'key_findings': '\n'.join(f"- {finding}" for finding in report_data['executive_summary']['key_findings']),
            'correlations_found': report_data['executive_summary']['correlations_found'],
            'analysis_confidence': report_data['executive_summary']['analysis_confidence']
        })
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        logger.info(f"Markdown report generated: {output_path}")
        return output_path
    
    def _get_html_template(self) -> str:
        """Get HTML report template."""
        return '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
        .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; padding: 15px; border-left: 4px solid #3498db; }}
        .threat-level {{ font-size: 1.2em; font-weight: bold; }}
        .critical {{ color: #e74c3c; }}
        .high {{ color: #f39c12; }}
        .medium {{ color: #f1c40f; }}
        .low {{ color: #27ae60; }}
        .data-section {{ background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }}
        pre {{ background: #f4f4f4; padding: 10px; border-radius: 3px; overflow-x: auto; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Forensic Timeline Analysis Report</h1>
        <p>Session ID: {session_id}</p>
        <p>Generated: {generated_at}</p>
    </div>
    
    <div class="section">
        <h2>Executive Summary</h2>
        <div class="data-section">
            <p><strong>Total Events Analyzed:</strong> {total_events}</p>
            <p><strong>Threat Level:</strong> <span class="threat-level {threat_level}">{threat_level}</span></p>
            <p><strong>Correlations Found:</strong> {correlations_found}</p>
            <p><strong>Analysis Confidence:</strong> {analysis_confidence}</p>
        </div>
        <h3>Key Findings</h3>
        <div class="data-section">
            {key_findings}
        </div>
    </div>
    
    <div class="section">
        <h2>Detailed Analysis Data</h2>
        <pre>{report_data_json}</pre>
    </div>
</body>
</html>'''
    
    def _get_markdown_template(self) -> str:
        """Get Markdown report template."""
        return '''# Forensic Timeline Analysis Report

**Session ID:** {session_id}  
**Generated:** {generated_at}

## Executive Summary

- **Total Events Analyzed:** {total_events}
- **Threat Level:** {threat_level}
- **Correlations Found:** {correlations_found}
- **Analysis Confidence:** {analysis_confidence}

### Key Findings

{key_findings}

## Analysis Complete

This report contains the results of comprehensive forensic timeline analysis.
For detailed technical data, please refer to the JSON export.
'''
