/* Simple FontAwesome icon replacements using Unicode symbols */
.fas, .fa {
    font-family: Arial, sans-serif;
    font-style: normal;
    font-weight: normal;
    display: inline-block;
}

.fa-upload:before { content: "⬆"; }
.fa-file:before { content: "📄"; }
.fa-clock:before { content: "🕐"; }
.fa-chart-line:before { content: "📈"; }
.fa-chart-pie:before { content: "📊"; }
.fa-chart-bar:before { content: "📊"; }
.fa-search:before { content: "🔍"; }
.fa-search-plus:before { content: "🔍+"; }
.fa-search-minus:before { content: "🔍-"; }
.fa-expand-arrows-alt:before { content: "⤢"; }
.fa-file-alt:before { content: "📝"; }
.fa-info-circle:before { content: "ℹ"; }
.fa-exclamation-circle:before { content: "⚠"; }
.fa-exclamation-triangle:before { content: "⚠"; }
.fa-check:before { content: "✓"; }
.fa-times:before { content: "✗"; }
.fa-cog:before { content: "⚙"; }
.fa-download:before { content: "⬇"; }
.fa-folder-open:before { content: "📂"; }
.fa-list:before { content: "📋"; }
.fa-link:before { content: "🔗"; }
.fa-tag:before { content: "🏷"; }
.fa-inbox:before { content: "📥"; }
.fa-spinner:before { content: "⟳"; }

/* Spinning animation for spinner */
.fa-spinner {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
