/* Forensic Timeline Analysis - Main Stylesheet */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --dark-color: #212529;
    --light-color: #f8f9fa;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-brand i {
    margin-right: 0.5rem;
    color: var(--info-color);
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Feature Icons */
.fa-3x {
    font-size: 3rem !important;
}

/* Timeline Styles (for future implementation) */
.timeline-container {
    position: relative;
    background: white;
    border: 1px solid #ddd;
    border-radius: 0.375rem;
    min-height: 400px;
}

.timeline-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: var(--secondary-color);
}

/* Upload Area Styles */
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.15s ease-in-out;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.upload-area.dragover {
    border-color: var(--success-color);
    background-color: rgba(25, 135, 84, 0.1);
}

/* Progress Bars */
.progress-container {
    margin: 1rem 0;
}

.progress {
    height: 1rem;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-pending {
    background-color: var(--warning-color);
}

.status-processing {
    background-color: var(--info-color);
    animation: pulse 1.5s infinite;
}

.status-complete {
    background-color: var(--success-color);
}

.status-error {
    background-color: var(--danger-color);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Event Type Colors */
.event-network-alert { color: var(--danger-color); }
.event-network-connection { color: var(--info-color); }
.event-process-creation { color: var(--warning-color); }
.event-authentication { color: var(--success-color); }
.event-system-log { color: var(--secondary-color); }
.event-c2-command { color: var(--danger-color); font-weight: bold; }

/* Responsive Design */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }

    .card-body {
        padding: 1rem;
    }

    .timeline-container {
        min-height: 300px;
    }
}

/* Utility Classes */
.text-muted-light {
    color: #6c757d !important;
}

.bg-light-gray {
    background-color: #f8f9fa !important;
}

.border-light-gray {
    border-color: #dee2e6 !important;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* File Upload Feedback */
.file-upload-feedback {
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.file-upload-success {
    color: var(--success-color);
}

.file-upload-error {
    color: var(--danger-color);
}