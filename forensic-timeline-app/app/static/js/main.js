/**
 * Main JavaScript file for Forensic Timeline Analysis
 * Provides common functionality across all pages
 */

// Global application object
window.ForensicApp = {
    // Configuration
    config: {
        apiBaseUrl: '/api',
        uploadUrl: '/api/upload',
        maxFileSize: 100 * 1024 * 1024, // 100MB
        allowedExtensions: ['.pcap', '.pcapng', '.evtx', '.log', '.txt', '.json', '.csv']
    },
    
    // Utility functions
    utils: {
        /**
         * Format file size in human readable format
         */
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        /**
         * Format timestamp in local format
         */
        formatTimestamp: function(timestamp) {
            return new Date(timestamp).toLocaleString();
        },
        
        /**
         * Show notification message
         */
        showNotification: function(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' : 
                              type === 'success' ? 'alert-success' : 
                              type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // Find container or create one
            let container = document.querySelector('.notification-container');
            if (!container) {
                container = document.createElement('div');
                container.className = 'notification-container position-fixed top-0 end-0 p-3';
                container.style.zIndex = '9999';
                document.body.appendChild(container);
            }
            
            container.insertAdjacentHTML('beforeend', alertHtml);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alert = container.querySelector('.alert:last-child');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        },
        
        /**
         * Make API request with error handling
         */
        apiRequest: function(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json'
                }
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            
            return fetch(url, finalOptions)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('API Request failed:', error);
                    this.showNotification(`API Error: ${error.message}`, 'error');
                    throw error;
                });
        }
    },
    
    // File upload functionality
    upload: {
        /**
         * Validate file before upload
         */
        validateFile: function(file) {
            const config = ForensicApp.config;
            
            // Check file size
            if (file.size > config.maxFileSize) {
                throw new Error(`File too large. Maximum size is ${ForensicApp.utils.formatFileSize(config.maxFileSize)}`);
            }
            
            // Check file extension
            const extension = '.' + file.name.split('.').pop().toLowerCase();
            if (!config.allowedExtensions.includes(extension)) {
                throw new Error(`File type not supported. Allowed types: ${config.allowedExtensions.join(', ')}`);
            }
            
            return true;
        },
        
        /**
         * Upload file with progress tracking
         */
        uploadFile: function(file, category, onProgress = null) {
            return new Promise((resolve, reject) => {
                try {
                    this.validateFile(file);
                } catch (error) {
                    reject(error);
                    return;
                }
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('category', category);
                
                const xhr = new XMLHttpRequest();
                
                // Progress tracking
                if (onProgress) {
                    xhr.upload.addEventListener('progress', (e) => {
                        if (e.lengthComputable) {
                            const percentComplete = (e.loaded / e.total) * 100;
                            onProgress(percentComplete);
                        }
                    });
                }
                
                // Success handler
                xhr.addEventListener('load', () => {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (error) {
                            reject(new Error('Invalid response from server'));
                        }
                    } else {
                        reject(new Error(`Upload failed: ${xhr.statusText}`));
                    }
                });
                
                // Error handler
                xhr.addEventListener('error', () => {
                    reject(new Error('Upload failed: Network error'));
                });
                
                // Start upload
                xhr.open('POST', ForensicApp.config.uploadUrl);
                xhr.send(formData);
            });
        }
    },
    
    // Session management
    session: {
        /**
         * Get current session info
         */
        getCurrentSession: function() {
            return ForensicApp.utils.apiRequest('/api/sessions/current');
        },
        
        /**
         * Get session status
         */
        getSessionStatus: function(sessionId) {
            return ForensicApp.utils.apiRequest(`/api/session/${sessionId}`);
        }
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Forensic Timeline Analysis - Main JS Loaded');
    
    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // Test API connectivity
    ForensicApp.utils.apiRequest('/api/health')
        .then(response => {
            console.log('API Health Check:', response);
        })
        .catch(error => {
            console.warn('API Health Check failed:', error);
        });
});

// Export for use in other scripts
window.ForensicApp = ForensicApp;
