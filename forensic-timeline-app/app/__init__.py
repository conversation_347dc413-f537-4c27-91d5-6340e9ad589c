"""
Forensic Timeline Analysis Application

A containerized forensic analysis tool that correlates network traffic captures
and system logs across baseline and attack scenarios.
"""

__version__ = "1.0.0"
__author__ = "Forensic Timeline Team"

from flask import Flask
from .config import Config
import logging
import os

def create_app(config_class=Config):
    """Application factory pattern for Flask app creation."""
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Configure logging
    if not app.debug and not app.testing:
        if not os.path.exists('logs'):
            os.mkdir('logs')

        file_handler = logging.FileHandler('logs/forensic_timeline.log')
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Forensic Timeline Analysis startup')

    # Register blueprints
    from .main import bp as main_bp
    app.register_blueprint(main_bp)

    from .api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    return app