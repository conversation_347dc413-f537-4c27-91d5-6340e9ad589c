{% extends "base.html" %}

{% block title %}Timeline Visualization - Forensic Timeline Analysis{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h3 class="mb-0">
                                <i class="fas fa-clock"></i>
                                Forensic Timeline Visualization
                            </h3>
                            <small>Session: {{ session_id }} | <span id="eventCount">Loading events...</span></small>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-light btn-sm" id="zoomInBtn">
                                    <i class="fas fa-search-plus"></i> Zoom In
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" id="zoomOutBtn">
                                    <i class="fas fa-search-minus"></i> Zoom Out
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" id="fitBtn">
                                    <i class="fas fa-expand-arrows-alt"></i> Fit All
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- Timeline Controls -->
                    <div class="bg-light p-3 border-bottom">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="row">
                                    <div class="col-auto">
                                        <label class="form-label small mb-0">Filter by Source:</label>
                                        <select class="form-select form-select-sm" id="sourceFilter">
                                            <option value="">All Sources</option>
                                            <option value="linux_syslog">Linux Syslog</option>
                                            <option value="windows_evtx">Windows EVTX</option>
                                            <option value="c2_log">C2 Logs</option>
                                            <option value="suricata">Suricata</option>
                                            <option value="zeek">Zeek</option>
                                        </select>
                                    </div>
                                    <div class="col-auto">
                                        <label class="form-label small mb-0">Filter by Category:</label>
                                        <select class="form-select form-select-sm" id="categoryFilter">
                                            <option value="">All Categories</option>
                                            <option value="baseline">Baseline</option>
                                            <option value="attack">Attack</option>
                                        </select>
                                    </div>
                                    <div class="col-auto">
                                        <label class="form-label small mb-0">Filter by Severity:</label>
                                        <select class="form-select form-select-sm" id="severityFilter">
                                            <option value="">All Severities</option>
                                            <option value="high">High</option>
                                            <option value="medium">Medium</option>
                                            <option value="low">Low</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group btn-group-sm" role="group">
                                    <input type="radio" class="btn-check" name="viewMode" id="timelineView" checked>
                                    <label class="btn btn-outline-primary" for="timelineView">Timeline View</label>
                                    
                                    <input type="radio" class="btn-check" name="viewMode" id="correlationView">
                                    <label class="btn btn-outline-primary" for="correlationView">Correlation View</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Analysis Statistics -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title mb-0" id="totalEventsCount">-</h4>
                                    <small>Total Events</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title mb-0" id="displayedEventsCount">-</h4>
                                    <small>Displayed Events</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title mb-0" id="timeRangeSpan">-</h4>
                                    <small>Time Range</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title mb-0" id="eventSourcesCount">-</h4>
                                    <small>Event Sources</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline Visualization -->
                    <div id="timelineContainer" style="height: 600px; width: 100%;"></div>

                    <!-- Pagination Controls -->
                    <div id="paginationControls" class="row mt-3" style="display: none;">
                        <div class="col-12">
                            <nav aria-label="Timeline pagination">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="pagination-info">
                                        <small class="text-muted" id="paginationInfo">Page 1 of 1</small>
                                    </div>
                                    <ul class="pagination pagination-sm mb-0">
                                        <li class="page-item" id="firstPageBtn">
                                            <a class="page-link" href="#" onclick="goToTimelinePage(1)">First</a>
                                        </li>
                                        <li class="page-item" id="prevPageBtn">
                                            <a class="page-link" href="#" onclick="goToPreviousPage()">Previous</a>
                                        </li>
                                        <li class="page-item active" id="currentPageBtn">
                                            <span class="page-link" id="currentPageNumber">1</span>
                                        </li>
                                        <li class="page-item" id="nextPageBtn">
                                            <a class="page-link" href="#" onclick="goToNextPage()">Next</a>
                                        </li>
                                        <li class="page-item" id="lastPageBtn">
                                            <a class="page-link" href="#" onclick="goToLastPage()">Last</a>
                                        </li>
                                    </ul>
                                    <div class="page-size-selector">
                                        <small class="text-muted me-2">Events per page:</small>
                                        <select class="form-select form-select-sm" style="width: auto; display: inline-block;" id="pageSizeSelector" onchange="changePageSize()">
                                            <option value="500">500</option>
                                            <option value="1000" selected>1000</option>
                                            <option value="2000">2000</option>
                                            <option value="5000">5000</option>
                                        </select>
                                    </div>
                                </div>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Details Modal -->
    <div class="modal fade" id="eventModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Event Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="eventDetails">
                        <!-- Event details will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="findCorrelationsBtn">
                        <i class="fas fa-search"></i> Find Correlations
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
         style="background-color: rgba(0,0,0,0.5); z-index: 9999; display: none;">
        <div class="text-center text-white">
            <div class="spinner-border mb-3" role="status"></div>
            <div id="loadingMessage">Loading timeline data...</div>
            <div class="mt-3">
                <button class="btn btn-outline-light btn-sm" onclick="retryLoading()" id="retryBtn" style="display: none;">
                    <i class="fas fa-redo"></i> Retry
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Vis.js Timeline Library (Local) -->
<script src="{{ url_for('static', filename='js/vis-timeline-graph2d.min.js') }}"></script>
<link href="{{ url_for('static', filename='css/vis-timeline-graph2d.min.css') }}" rel="stylesheet" type="text/css" />

<script>
let timeline;
let timelineData = {
    events: [],
    groups: []
};
let filteredData = {
    events: [],
    groups: []
};

// Pagination state
let currentPage = 1;
let pageSize = 1000;
let totalEvents = 0;
let totalPages = 0;

// Initialize timeline on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeTimeline();
    loadTimelineData(1, 1000); // Start with page 1, 1000 events per page
    setupEventListeners();
});

function initializeTimeline() {
    const container = document.getElementById('timelineContainer');

    // Debug: Check if container exists
    if (!container) {
        console.error('Timeline container not found!');
        showError('Timeline container element not found');
        return;
    }

    // Debug: Check if vis.js is loaded
    if (typeof vis === 'undefined') {
        console.error('vis.js library not loaded!');
        showError('Timeline library not loaded. Please refresh the page.');
        return;
    }

    console.log('Initializing timeline with vis.js version:', vis.version || 'unknown');

    // Timeline options optimized for large datasets
    const options = {
        width: '100%',
        height: '600px',
        margin: {
            item: 10,
            axis: 40
        },
        orientation: 'top',
        stack: true,
        showCurrentTime: false,
        zoomable: true,
        moveable: true,
        selectable: true,
        multiselect: false,
        // Performance optimizations for large datasets
        maxHeight: 600,
        verticalScroll: true,
        horizontalScroll: true,
        tooltip: {
            followMouse: true,
            overflowMethod: 'cap'
        },
        format: {
            minorLabels: {
                millisecond:'SSS',
                second:     's',
                minute:     'HH:mm',
                hour:       'HH:mm',
                weekday:    'ddd D',
                day:        'D',
                week:       'w',
                month:      'MMM',
                year:       'YYYY'
            },
            majorLabels: {
                millisecond:'HH:mm:ss',
                second:     'D MMMM HH:mm',
                minute:     'ddd D MMMM',
                hour:       'ddd D MMMM',
                weekday:    'MMMM YYYY',
                day:        'MMMM YYYY',
                week:       'MMMM YYYY',
                month:      'YYYY',
                year:       ''
            }
        }
    };

    try {
        // Create timeline
        timeline = new vis.Timeline(container, [], [], options);
        console.log('Timeline initialized successfully');
    } catch (error) {
        console.error('Error creating timeline:', error);
        showError('Failed to initialize timeline: ' + error.message);
        return;
    }
    
    // Event handlers
    timeline.on('select', function(properties) {
        if (properties.items.length > 0) {
            showEventDetails(properties.items[0]);
        }
    });

    timeline.on('doubleClick', function(properties) {
        if (properties.item) {
            showEventDetails(properties.item);
        }
    });

    // Add ready event listener to detect when timeline is fully rendered
    timeline.on('changed', function() {
        console.log('Timeline changed event fired');
    });

    // Add error handling for timeline rendering issues
    timeline.on('rangechange', function() {
        console.log('Timeline range changed');
    });
}

function loadTimelineData(page = 1, size = 1000) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const loadingMessage = document.getElementById('loadingMessage');
    const retryBtn = document.getElementById('retryBtn');

    loadingOverlay.style.display = 'flex';
    loadingMessage.textContent = 'Loading timeline data...';
    retryBtn.style.display = 'none';

    // Update pagination state
    currentPage = page;
    pageSize = size;

    // Show retry button after 5 seconds
    const retryTimeout = setTimeout(() => {
        if (loadingOverlay.style.display === 'flex') {
            retryBtn.style.display = 'inline-block';
            loadingMessage.textContent = 'Loading is taking longer than expected...';
        }
    }, 5000);

    // Shorter timeout for faster feedback
    const timeoutId = setTimeout(() => {
        clearTimeout(retryTimeout);
        loadingOverlay.style.display = 'none';
        showError('Timeline loading timed out after 10 seconds. Please try again.');
    }, 10000); // 10 second timeout

    // Build URL with pagination parameters
    const url = `/api/timeline/{{ session_id }}?page=${page}&page_size=${size}`;
    console.log(`Loading timeline data: ${url}`);

    // Use fetch with AbortController for better timeout control
    const controller = new AbortController();
    const fetchTimeout = setTimeout(() => controller.abort(), 8000); // 8 second fetch timeout

    fetch(url, { signal: controller.signal })
        .then(response => {
            clearTimeout(timeoutId);
            clearTimeout(fetchTimeout);
            clearTimeout(retryTimeout);

            console.log(`API Response: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Timeline data loaded successfully:', {
                events: data.events?.length || 0,
                groups: data.groups?.length || 0,
                pagination: data.pagination
            });

            // Update pagination info from API response
            if (data.pagination) {
                totalEvents = data.pagination.total_events;
                totalPages = data.pagination.total_pages;
                currentPage = data.pagination.page;
                pageSize = data.pagination.page_size;
                console.log(`Pagination: page ${currentPage}/${totalPages}, ${data.events.length} events of ${totalEvents} total`);
            } else {
                console.warn('No pagination data received from API');
                totalEvents = data.events?.length || 0;
                totalPages = 1;
            }

            timelineData = data;

            // Use the paginated data directly
            filteredData = {
                events: data.events || [],
                groups: data.groups || []
            };

            console.log('Calling updateTimeline...');
            updateTimeline();

            // Hide loading overlay
            loadingOverlay.style.display = 'none';
        })
        .catch(error => {
            clearTimeout(timeoutId);
            clearTimeout(fetchTimeout);
            clearTimeout(retryTimeout);

            console.error('Error loading timeline data:', error);
            loadingOverlay.style.display = 'none';

            if (error.name === 'AbortError') {
                showError('Timeline loading was cancelled due to timeout. Please try again.');
            } else {
                showError('Failed to load timeline data: ' + error.message);
            }
        });
}

function retryLoading() {
    console.log('Retrying timeline data load...');
    loadTimelineData(currentPage, pageSize);
}
}

function updateTimeline() {
    try {
        console.log('Updating timeline with', filteredData.events.length, 'events');

        // Check if timeline is initialized
        if (!timeline) {
            console.error('Timeline not initialized!');
            showError('Timeline not initialized. Please refresh the page.');
            return;
        }

        // Events are already paginated by the API
        let eventsToShow = filteredData.events;

        console.log(`DEBUG: Displaying ${eventsToShow.length} events from API (page ${currentPage}/${totalPages})`);

        // Show pagination controls if we have multiple pages
        if (totalPages > 1) {
            console.log(`DEBUG: Showing pagination controls (${totalPages} pages total)`);
            showInfo(`Showing page ${currentPage} (${eventsToShow.length} events) of ${totalPages} total pages. Total events: ${totalEvents.toLocaleString()}`);
            updatePaginationControls(currentPage, totalPages, totalEvents);
        } else {
            console.log(`DEBUG: Hiding pagination controls (single page)`);
            showInfo(`Displaying all ${totalEvents.toLocaleString()} network events.`);
            hidePaginationControls();
        }

        console.log(`Creating vis.js DataSets with ${eventsToShow.length} events...`);

        console.log(`Creating vis.js DataSets with ${eventsToShow.length} events...`);

        try {
            // Validate data before creating datasets
            if (eventsToShow.length > 0) {
                console.log('Sample event data:', eventsToShow[0]);
            }
            if (filteredData.groups.length > 0) {
                console.log('Sample group data:', filteredData.groups[0]);
            }

            // Create new datasets
            const items = new vis.DataSet(eventsToShow);
            const groups = new vis.DataSet(filteredData.groups);

            console.log(`Items DataSet created with ${items.length} items`);
            console.log(`Groups DataSet created with ${groups.length} groups`);

            // Set timeline data
            timeline.setData({
                items: items,
                groups: groups
            });

            console.log('Timeline data set successfully');

            // Fit timeline after a short delay
            setTimeout(() => {
                try {
                    timeline.fit();
                    console.log('Timeline fit completed');
                } catch (e) {
                    console.warn('Timeline fit failed:', e);
                }

                // Update the info message after successful rendering
                if (totalPages > 1) {
                    showInfo(`Showing page ${currentPage} (${eventsToShow.length} events) of ${totalPages} total pages. Total events: ${totalEvents.toLocaleString()}`);
                } else {
                    showInfo(`Displaying all ${totalEvents.toLocaleString()} network events.`);
                }
            }, 100);

        } catch (e) {
            console.error('Error rendering timeline:', e);
            showError('Failed to render timeline: ' + e.message);
        }

        // Update event count display
        updateEventCount(eventsToShow.length, filteredData.events.length);

        // Update statistics
        updateStatistics(eventsToShow, filteredData.events, filteredData.groups);

    } catch (error) {
        console.error('Error updating timeline:', error);
        showError('Failed to update timeline visualization');
    }
}

function setupEventListeners() {
    // Filter controls
    document.getElementById('sourceFilter').addEventListener('change', applyFilters);
    document.getElementById('categoryFilter').addEventListener('change', applyFilters);
    document.getElementById('severityFilter').addEventListener('change', applyFilters);
    
    // Timeline controls
    document.getElementById('zoomInBtn').addEventListener('click', () => timeline.zoomIn(0.5));
    document.getElementById('zoomOutBtn').addEventListener('click', () => timeline.zoomOut(0.5));
    document.getElementById('fitBtn').addEventListener('click', () => timeline.fit());
    
    // View mode controls
    document.getElementById('timelineView').addEventListener('change', function() {
        if (this.checked) {
            switchToTimelineView();
        }
    });
    
    document.getElementById('correlationView').addEventListener('change', function() {
        if (this.checked) {
            switchToCorrelationView();
        }
    });
    
    // Find correlations button
    document.getElementById('findCorrelationsBtn').addEventListener('click', findEventCorrelations);
}

function applyFilters() {
    const sourceFilter = document.getElementById('sourceFilter').value;
    const categoryFilter = document.getElementById('categoryFilter').value;
    const severityFilter = document.getElementById('severityFilter').value;
    
    filteredData.events = timelineData.events.filter(event => {
        if (sourceFilter && event.source !== sourceFilter) return false;
        if (categoryFilter && event.category !== categoryFilter) return false;
        if (severityFilter && event.severity !== severityFilter) return false;
        return true;
    });
    
    updateTimeline();
}

function showEventDetails(eventId) {
    const event = timelineData.events.find(e => e.id === eventId);
    if (!event) return;
    
    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Timestamp:</strong></td><td>${new Date(event.start).toLocaleString()}</td></tr>
                    <tr><td><strong>Source:</strong></td><td><span class="badge bg-info">${event.source || 'Unknown'}</span></td></tr>
                    <tr><td><strong>Category:</strong></td><td><span class="badge ${event.category === 'attack' ? 'bg-danger' : 'bg-success'}">${event.category || 'Unknown'}</span></td></tr>
                    <tr><td><strong>Severity:</strong></td><td><span class="badge ${getSeverityBadgeClass(event.severity)}">${event.severity || 'Unknown'}</span></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Description</h6>
                <p>${event.content}</p>
                ${event.metadata ? '<h6>Metadata</h6><pre class="small">' + JSON.stringify(event.metadata, null, 2) + '</pre>' : ''}
            </div>
        </div>
    `;
    
    document.getElementById('eventDetails').innerHTML = detailsHtml;
    
    // Store current event ID for correlation search
    document.getElementById('findCorrelationsBtn').dataset.eventId = eventId;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('eventModal'));
    modal.show();
}

function getSeverityBadgeClass(severity) {
    switch(severity) {
        case 'high': return 'bg-danger';
        case 'medium': return 'bg-warning';
        case 'low': return 'bg-secondary';
        default: return 'bg-light text-dark';
    }
}

function switchToTimelineView() {
    // Implementation for timeline view
    console.log('Switched to timeline view');
}

function switchToCorrelationView() {
    // Implementation for correlation view
    console.log('Switched to correlation view');
}

function findEventCorrelations() {
    const eventId = document.getElementById('findCorrelationsBtn').dataset.eventId;
    if (!eventId) return;
    
    // TODO: Implement correlation search
    console.log('Finding correlations for event:', eventId);
    alert('Correlation search will be implemented in the next phase');
}

function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
}

function showWarning(message) {
    const alertHtml = `
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
}

function showInfo(message) {
    const alertHtml = `
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
}

function updateEventCount(showing, total) {
    const countElement = document.getElementById('eventCount');
    if (countElement) {
        if (showing === total) {
            countElement.textContent = `Showing ${total} events`;
        } else {
            countElement.textContent = `Showing ${showing} of ${total} events`;
        }
    }
}

function updateStatistics(displayedEvents, allEvents, groups) {
    // Update total events count
    document.getElementById('totalEventsCount').textContent = allEvents.length.toLocaleString();

    // Update displayed events count
    document.getElementById('displayedEventsCount').textContent = displayedEvents.length.toLocaleString();

    // Calculate time range
    if (allEvents.length > 0) {
        const timestamps = allEvents.map(e => new Date(e.start)).sort((a, b) => a - b);
        const startTime = timestamps[0];
        const endTime = timestamps[timestamps.length - 1];
        const duration = endTime - startTime;

        let timeRangeText;
        if (duration < 60000) { // Less than 1 minute
            timeRangeText = `${Math.round(duration / 1000)}s`;
        } else if (duration < 3600000) { // Less than 1 hour
            timeRangeText = `${Math.round(duration / 60000)}m`;
        } else if (duration < 86400000) { // Less than 1 day
            timeRangeText = `${Math.round(duration / 3600000)}h`;
        } else {
            timeRangeText = `${Math.round(duration / 86400000)}d`;
        }

        document.getElementById('timeRangeSpan').textContent = timeRangeText;
    } else {
        document.getElementById('timeRangeSpan').textContent = '-';
    }

    // Update event sources count
    document.getElementById('eventSourcesCount').textContent = groups.length;
}

// Pagination functions
function getOptimalPageSize(totalEvents) {
    const pageSizeSelector = document.getElementById('pageSizeSelector');
    if (pageSizeSelector) {
        const selectedSize = parseInt(pageSizeSelector.value);
        console.log(`DEBUG: Using selected page size: ${selectedSize}`);
        return selectedSize;
    }

    // Default page sizes based on dataset size
    let pageSize;
    if (totalEvents > 10000) pageSize = 1000;
    else if (totalEvents > 5000) pageSize = 500;
    else if (totalEvents > 1000) pageSize = 250;
    else pageSize = Math.min(totalEvents, 100); // Show all if small dataset, but max 100

    console.log(`DEBUG: Calculated page size for ${totalEvents} events: ${pageSize}`);
    return pageSize;
}

function updatePaginationControls(currentPage, totalPages, totalEvents) {
    const paginationControls = document.getElementById('paginationControls');
    const paginationInfo = document.getElementById('paginationInfo');
    const currentPageNumber = document.getElementById('currentPageNumber');
    const firstPageBtn = document.getElementById('firstPageBtn');
    const prevPageBtn = document.getElementById('prevPageBtn');
    const nextPageBtn = document.getElementById('nextPageBtn');
    const lastPageBtn = document.getElementById('lastPageBtn');

    // Show pagination controls
    paginationControls.style.display = 'block';

    // Update pagination info
    const pageSize = getOptimalPageSize(totalEvents);
    const startEvent = (currentPage - 1) * pageSize + 1;
    const endEvent = Math.min(currentPage * pageSize, totalEvents);
    paginationInfo.textContent = `Showing ${startEvent}-${endEvent} of ${totalEvents.toLocaleString()} events (Page ${currentPage} of ${totalPages})`;

    // Update current page number
    currentPageNumber.textContent = currentPage;

    // Enable/disable navigation buttons
    firstPageBtn.classList.toggle('disabled', currentPage === 1);
    prevPageBtn.classList.toggle('disabled', currentPage === 1);
    nextPageBtn.classList.toggle('disabled', currentPage === totalPages);
    lastPageBtn.classList.toggle('disabled', currentPage === totalPages);

    // Store pagination state
    window.currentTimelinePage = currentPage;
    window.totalTimelinePages = totalPages;
}

function hidePaginationControls() {
    const paginationControls = document.getElementById('paginationControls');
    paginationControls.style.display = 'none';
}

function goToTimelinePage(page) {
    if (page < 1 || page > totalPages) return;

    console.log(`Navigating to page ${page}`);
    loadTimelineData(page, pageSize);
}

function goToPreviousPage() {
    if (currentPage > 1) {
        goToTimelinePage(currentPage - 1);
    }
}

function goToNextPage() {
    if (currentPage < totalPages) {
        goToTimelinePage(currentPage + 1);
    }
}

function goToLastPage() {
    goToTimelinePage(totalPages);
}

function changePageSize() {
    const pageSizeSelector = document.getElementById('pageSizeSelector');
    const newPageSize = parseInt(pageSizeSelector.value) || 1000;

    console.log(`Changing page size to ${newPageSize}`);
    // Reset to first page when changing page size
    loadTimelineData(1, newPageSize);
}
</script>
{% endblock %}
