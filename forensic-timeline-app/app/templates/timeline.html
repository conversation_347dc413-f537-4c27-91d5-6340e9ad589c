{% extends "base.html" %}

{% block title %}Timeline Visualization - Forensic Timeline Analysis{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h3 class="mb-0">
                                <i class="fas fa-clock"></i>
                                Forensic Timeline Visualization
                            </h3>
                            <small>Session: {{ session_id }} | <span id="eventCount">Loading events...</span></small>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-light btn-sm" id="zoomInBtn">
                                    <i class="fas fa-search-plus"></i> Zoom In
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" id="zoomOutBtn">
                                    <i class="fas fa-search-minus"></i> Zoom Out
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" id="fitBtn">
                                    <i class="fas fa-expand-arrows-alt"></i> Fit All
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- Timeline Controls -->
                    <div class="bg-light p-3 border-bottom">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="row">
                                    <div class="col-auto">
                                        <label class="form-label small mb-0">Filter by Source:</label>
                                        <select class="form-select form-select-sm" id="sourceFilter">
                                            <option value="">All Sources</option>
                                            <option value="linux_syslog">Linux Syslog</option>
                                            <option value="windows_evtx">Windows EVTX</option>
                                            <option value="c2_log">C2 Logs</option>
                                            <option value="suricata">Suricata</option>
                                            <option value="zeek">Zeek</option>
                                        </select>
                                    </div>
                                    <div class="col-auto">
                                        <label class="form-label small mb-0">Filter by Category:</label>
                                        <select class="form-select form-select-sm" id="categoryFilter">
                                            <option value="">All Categories</option>
                                            <option value="baseline">Baseline</option>
                                            <option value="attack">Attack</option>
                                        </select>
                                    </div>
                                    <div class="col-auto">
                                        <label class="form-label small mb-0">Filter by Severity:</label>
                                        <select class="form-select form-select-sm" id="severityFilter">
                                            <option value="">All Severities</option>
                                            <option value="high">High</option>
                                            <option value="medium">Medium</option>
                                            <option value="low">Low</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group btn-group-sm" role="group">
                                    <input type="radio" class="btn-check" name="viewMode" id="timelineView" checked>
                                    <label class="btn btn-outline-primary" for="timelineView">Timeline View</label>
                                    
                                    <input type="radio" class="btn-check" name="viewMode" id="correlationView">
                                    <label class="btn btn-outline-primary" for="correlationView">Correlation View</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Analysis Statistics -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title mb-0" id="totalEventsCount">-</h4>
                                    <small>Total Events</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title mb-0" id="displayedEventsCount">-</h4>
                                    <small>Displayed Events</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title mb-0" id="timeRangeSpan">-</h4>
                                    <small>Time Range</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title mb-0" id="eventSourcesCount">-</h4>
                                    <small>Event Sources</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline Visualization -->
                    <div id="timelineContainer" style="height: 600px; width: 100%;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Details Modal -->
    <div class="modal fade" id="eventModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Event Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="eventDetails">
                        <!-- Event details will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="findCorrelationsBtn">
                        <i class="fas fa-search"></i> Find Correlations
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
         style="background-color: rgba(0,0,0,0.5); z-index: 9999; display: none;">
        <div class="text-center text-white">
            <div class="spinner-border mb-3" role="status"></div>
            <div>Loading timeline data...</div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Vis.js Timeline Library (Local) -->
<script src="{{ url_for('static', filename='js/vis-timeline-graph2d.min.js') }}"></script>
<link href="{{ url_for('static', filename='css/vis-timeline-graph2d.min.css') }}" rel="stylesheet" type="text/css" />

<script>
let timeline;
let timelineData = {
    events: [],
    groups: []
};
let filteredData = {
    events: [],
    groups: []
};

// Initialize timeline on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeTimeline();
    loadTimelineData();
    setupEventListeners();
});

function initializeTimeline() {
    const container = document.getElementById('timelineContainer');
    
    // Timeline options
    const options = {
        width: '100%',
        height: '600px',
        margin: {
            item: 10,
            axis: 40
        },
        orientation: 'top',
        stack: true,
        showCurrentTime: false,
        zoomable: true,
        moveable: true,
        selectable: true,
        multiselect: false,
        tooltip: {
            followMouse: true,
            overflowMethod: 'cap'
        },
        format: {
            minorLabels: {
                millisecond:'SSS',
                second:     's',
                minute:     'HH:mm',
                hour:       'HH:mm',
                weekday:    'ddd D',
                day:        'D',
                week:       'w',
                month:      'MMM',
                year:       'YYYY'
            },
            majorLabels: {
                millisecond:'HH:mm:ss',
                second:     'D MMMM HH:mm',
                minute:     'ddd D MMMM',
                hour:       'ddd D MMMM',
                weekday:    'MMMM YYYY',
                day:        'MMMM YYYY',
                week:       'MMMM YYYY',
                month:      'YYYY',
                year:       ''
            }
        }
    };
    
    // Create timeline
    timeline = new vis.Timeline(container, [], [], options);
    
    // Event handlers
    timeline.on('select', function(properties) {
        if (properties.items.length > 0) {
            showEventDetails(properties.items[0]);
        }
    });
    
    timeline.on('doubleClick', function(properties) {
        if (properties.item) {
            showEventDetails(properties.item);
        }
    });
}

function loadTimelineData() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.style.display = 'flex';

    // Add timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
        loadingOverlay.style.display = 'none';
        showError('Timeline loading timed out. Please try again.');
    }, 30000); // 30 second timeout

    fetch(`/api/timeline/{{ session_id }}`)
        .then(response => {
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Timeline data loaded:', data);
            timelineData = data;

            // Optimize: Only deep copy if we have events to avoid performance issues
            if (data.events && data.events.length > 0) {
                // For large datasets, use a more efficient copy method
                filteredData = {
                    events: [...data.events],
                    groups: [...data.groups]
                };
            } else {
                filteredData = { events: [], groups: [] };
            }

            updateTimeline();
            loadingOverlay.style.display = 'none';
        })
        .catch(error => {
            clearTimeout(timeoutId);
            console.error('Error loading timeline data:', error);
            loadingOverlay.style.display = 'none';
            showError('Failed to load timeline data: ' + error.message);
        });
}

function updateTimeline() {
    try {
        console.log('Updating timeline with', filteredData.events.length, 'events');

        // Performance optimization: Limit events for very large datasets
        let eventsToShow = filteredData.events;
        const totalEvents = eventsToShow.length;

        if (totalEvents > 5000) {
            console.warn(`Large dataset detected (${totalEvents} events), showing first 5000 events`);
            eventsToShow = eventsToShow.slice(0, 5000);
            showWarning(`Large dataset detected: ${totalEvents.toLocaleString()} total events. Showing first 5,000 for performance. Use filters to view specific time ranges or event types.`);
        } else if (totalEvents > 1000) {
            console.log(`Medium dataset detected (${totalEvents} events), showing all events`);
            showInfo(`Displaying ${totalEvents.toLocaleString()} network events. Timeline may take a moment to render.`);
        }

        const items = new vis.DataSet(eventsToShow);
        const groups = new vis.DataSet(filteredData.groups);

        timeline.setItems(items);
        timeline.setGroups(groups);

        // Fit timeline to show all events with a small delay to ensure rendering is complete
        if (eventsToShow.length > 0) {
            setTimeout(() => {
                try {
                    timeline.fit();
                } catch (e) {
                    console.warn('Timeline fit failed:', e);
                }
            }, 100);
        }

        // Update event count display
        updateEventCount(eventsToShow.length, filteredData.events.length);

        // Update statistics
        updateStatistics(eventsToShow, filteredData.events, filteredData.groups);

    } catch (error) {
        console.error('Error updating timeline:', error);
        showError('Failed to update timeline visualization');
    }
}

function setupEventListeners() {
    // Filter controls
    document.getElementById('sourceFilter').addEventListener('change', applyFilters);
    document.getElementById('categoryFilter').addEventListener('change', applyFilters);
    document.getElementById('severityFilter').addEventListener('change', applyFilters);
    
    // Timeline controls
    document.getElementById('zoomInBtn').addEventListener('click', () => timeline.zoomIn(0.5));
    document.getElementById('zoomOutBtn').addEventListener('click', () => timeline.zoomOut(0.5));
    document.getElementById('fitBtn').addEventListener('click', () => timeline.fit());
    
    // View mode controls
    document.getElementById('timelineView').addEventListener('change', function() {
        if (this.checked) {
            switchToTimelineView();
        }
    });
    
    document.getElementById('correlationView').addEventListener('change', function() {
        if (this.checked) {
            switchToCorrelationView();
        }
    });
    
    // Find correlations button
    document.getElementById('findCorrelationsBtn').addEventListener('click', findEventCorrelations);
}

function applyFilters() {
    const sourceFilter = document.getElementById('sourceFilter').value;
    const categoryFilter = document.getElementById('categoryFilter').value;
    const severityFilter = document.getElementById('severityFilter').value;
    
    filteredData.events = timelineData.events.filter(event => {
        if (sourceFilter && event.source !== sourceFilter) return false;
        if (categoryFilter && event.category !== categoryFilter) return false;
        if (severityFilter && event.severity !== severityFilter) return false;
        return true;
    });
    
    updateTimeline();
}

function showEventDetails(eventId) {
    const event = timelineData.events.find(e => e.id === eventId);
    if (!event) return;
    
    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Timestamp:</strong></td><td>${new Date(event.start).toLocaleString()}</td></tr>
                    <tr><td><strong>Source:</strong></td><td><span class="badge bg-info">${event.source || 'Unknown'}</span></td></tr>
                    <tr><td><strong>Category:</strong></td><td><span class="badge ${event.category === 'attack' ? 'bg-danger' : 'bg-success'}">${event.category || 'Unknown'}</span></td></tr>
                    <tr><td><strong>Severity:</strong></td><td><span class="badge ${getSeverityBadgeClass(event.severity)}">${event.severity || 'Unknown'}</span></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Description</h6>
                <p>${event.content}</p>
                ${event.metadata ? '<h6>Metadata</h6><pre class="small">' + JSON.stringify(event.metadata, null, 2) + '</pre>' : ''}
            </div>
        </div>
    `;
    
    document.getElementById('eventDetails').innerHTML = detailsHtml;
    
    // Store current event ID for correlation search
    document.getElementById('findCorrelationsBtn').dataset.eventId = eventId;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('eventModal'));
    modal.show();
}

function getSeverityBadgeClass(severity) {
    switch(severity) {
        case 'high': return 'bg-danger';
        case 'medium': return 'bg-warning';
        case 'low': return 'bg-secondary';
        default: return 'bg-light text-dark';
    }
}

function switchToTimelineView() {
    // Implementation for timeline view
    console.log('Switched to timeline view');
}

function switchToCorrelationView() {
    // Implementation for correlation view
    console.log('Switched to correlation view');
}

function findEventCorrelations() {
    const eventId = document.getElementById('findCorrelationsBtn').dataset.eventId;
    if (!eventId) return;
    
    // TODO: Implement correlation search
    console.log('Finding correlations for event:', eventId);
    alert('Correlation search will be implemented in the next phase');
}

function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
}

function showWarning(message) {
    const alertHtml = `
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
}

function showInfo(message) {
    const alertHtml = `
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
}

function updateEventCount(showing, total) {
    const countElement = document.getElementById('eventCount');
    if (countElement) {
        if (showing === total) {
            countElement.textContent = `Showing ${total} events`;
        } else {
            countElement.textContent = `Showing ${showing} of ${total} events`;
        }
    }
}

function updateStatistics(displayedEvents, allEvents, groups) {
    // Update total events count
    document.getElementById('totalEventsCount').textContent = allEvents.length.toLocaleString();

    // Update displayed events count
    document.getElementById('displayedEventsCount').textContent = displayedEvents.length.toLocaleString();

    // Calculate time range
    if (allEvents.length > 0) {
        const timestamps = allEvents.map(e => new Date(e.start)).sort((a, b) => a - b);
        const startTime = timestamps[0];
        const endTime = timestamps[timestamps.length - 1];
        const duration = endTime - startTime;

        let timeRangeText;
        if (duration < 60000) { // Less than 1 minute
            timeRangeText = `${Math.round(duration / 1000)}s`;
        } else if (duration < 3600000) { // Less than 1 hour
            timeRangeText = `${Math.round(duration / 60000)}m`;
        } else if (duration < 86400000) { // Less than 1 day
            timeRangeText = `${Math.round(duration / 3600000)}h`;
        } else {
            timeRangeText = `${Math.round(duration / 86400000)}d`;
        }

        document.getElementById('timeRangeSpan').textContent = timeRangeText;
    } else {
        document.getElementById('timeRangeSpan').textContent = '-';
    }

    // Update event sources count
    document.getElementById('eventSourcesCount').textContent = groups.length;
}
</script>
{% endblock %}
