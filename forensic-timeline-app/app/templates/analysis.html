{% extends "base.html" %}

{% block title %}Analysis Results - Forensic Timeline Analysis{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h3 class="mb-0">
                                <i class="fas fa-chart-line"></i>
                                Analysis Results
                            </h3>
                            <small>Session: {{ session_id }}</small>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <a href="/timeline/{{ session_id }}" class="btn btn-outline-light">
                                    <i class="fas fa-clock"></i> View Timeline
                                </a>
                                <a href="/report/{{ session_id }}" class="btn btn-outline-light">
                                    <i class="fas fa-file-alt"></i> Generate Report
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Analysis Status -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h5 class="mb-0">
                                            <i class="fas fa-info-circle"></i>
                                            Analysis Status: <span id="analysisStatus">{{ session.status|default('Unknown') }}</span>
                                        </h5>
                                        <p class="mb-0" id="analysisMessage">{{ session.message|default('Analysis in progress...') }}</p>
                                    </div>
                                    <div class="col-auto">
                                        <div class="progress" style="width: 200px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ session.progress|default(0) }}%" 
                                                 id="progressBar">
                                                {{ session.progress|default(0) }}%
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-upload fa-2x mb-2"></i>
                                    <h4 id="totalFiles">{{ session.uploaded_files|length|default(0) }}</h4>
                                    <p class="mb-0">Files Uploaded</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-list fa-2x mb-2"></i>
                                    <h4 id="totalEvents">{{ session.total_events|default(0) }}</h4>
                                    <p class="mb-0">Events Processed</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-link fa-2x mb-2"></i>
                                    <h4 id="totalCorrelations">0</h4>
                                    <p class="mb-0">Correlations Found</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                    <h4 id="highSeverityEvents">0</h4>
                                    <p class="mb-0">High Severity Events</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Uploaded Files -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-folder-open"></i>
                                        Uploaded Files
                                    </h5>
                                </div>
                                <div class="card-body">
                                    {% if session.uploaded_files %}
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Filename</th>
                                                    <th>Type</th>
                                                    <th>Category</th>
                                                    <th>Size</th>
                                                    <th>Upload Time</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for file in session.uploaded_files %}
                                                <tr>
                                                    <td>
                                                        <i class="fas fa-file"></i>
                                                        {{ file.filename }}
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">{{ file.file_type }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ 'bg-danger' if file.category == 'attack' else 'bg-success' }}">
                                                            {{ file.category }}
                                                        </span>
                                                    </td>
                                                    <td>{{ (file.size_bytes / 1024 / 1024)|round(2) }} MB</td>
                                                    <td>{{ file.upload_time }}</td>
                                                    <td>
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check"></i> Processed
                                                        </span>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <p>No files uploaded yet</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Event Summary Charts -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-pie"></i>
                                        Events by Source
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="sourceChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-bar"></i>
                                        Events by Severity
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="severityChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Events -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-clock"></i>
                                        Recent Events
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div id="recentEvents">
                                        <div class="text-center text-muted">
                                            <div class="spinner-border" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2">Loading recent events...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js for visualizations (Local) -->
<script src="{{ url_for('static', filename='js/chart.min.js') }}"></script>

<script>
let sourceChart, severityChart;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadAnalysisData();
    
    // Auto-refresh every 5 seconds if analysis is in progress
    if (isAnalysisInProgress()) {
        setInterval(refreshAnalysisStatus, 5000);
    }
});

function initializeCharts() {
    // Source distribution chart
    const sourceCtx = document.getElementById('sourceChart').getContext('2d');
    sourceChart = new Chart(sourceCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Severity distribution chart
    const severityCtx = document.getElementById('severityChart').getContext('2d');
    severityChart = new Chart(severityCtx, {
        type: 'bar',
        data: {
            labels: ['High', 'Medium', 'Low'],
            datasets: [{
                label: 'Events',
                data: [0, 0, 0],
                backgroundColor: ['#dc3545', '#ffc107', '#6c757d']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

function loadAnalysisData() {
    fetch(`/api/session/{{ session_id }}`)
        .then(response => response.json())
        .then(data => {
            updateAnalysisStatus(data);
            loadTimelineData();
        })
        .catch(error => {
            console.error('Error loading analysis data:', error);
            showError('Failed to load analysis data');
        });
}

function loadTimelineData() {
    fetch(`/api/timeline/{{ session_id }}`)
        .then(response => response.json())
        .then(data => {
            updateCharts(data);
            updateRecentEvents(data.events);
        })
        .catch(error => {
            console.error('Error loading timeline data:', error);
            // Use sample data for demonstration
            updateChartsWithSampleData();
            updateRecentEventsWithSampleData();
        });
}

function updateAnalysisStatus(sessionData) {
    document.getElementById('analysisStatus').textContent = sessionData.status || 'Unknown';
    document.getElementById('analysisMessage').textContent = sessionData.message || 'Analysis in progress...';
    
    const progress = sessionData.progress || 0;
    const progressBar = document.getElementById('progressBar');
    progressBar.style.width = progress + '%';
    progressBar.textContent = progress + '%';
    
    // Update statistics
    document.getElementById('totalFiles').textContent = sessionData.uploaded_files ? sessionData.uploaded_files.length : 0;
    document.getElementById('totalEvents').textContent = sessionData.total_events || 0;
}

function updateCharts(timelineData) {
    // Update source chart
    const sourceCounts = {};
    timelineData.events.forEach(event => {
        const source = event.source || 'Unknown';
        sourceCounts[source] = (sourceCounts[source] || 0) + 1;
    });
    
    sourceChart.data.labels = Object.keys(sourceCounts);
    sourceChart.data.datasets[0].data = Object.values(sourceCounts);
    sourceChart.update();
    
    // Update severity chart
    const severityCounts = { high: 0, medium: 0, low: 0 };
    timelineData.events.forEach(event => {
        const severity = event.severity || 'low';
        if (severityCounts.hasOwnProperty(severity)) {
            severityCounts[severity]++;
        }
    });
    
    severityChart.data.datasets[0].data = [severityCounts.high, severityCounts.medium, severityCounts.low];
    severityChart.update();
    
    // Update high severity count
    document.getElementById('highSeverityEvents').textContent = severityCounts.high;
}

function updateChartsWithSampleData() {
    // Sample data for demonstration
    sourceChart.data.labels = ['Linux Syslog', 'C2 Logs', 'Windows EVTX'];
    sourceChart.data.datasets[0].data = [137, 14, 0];
    sourceChart.update();
    
    severityChart.data.datasets[0].data = [26, 82, 43];
    severityChart.update();
    
    document.getElementById('totalCorrelations').textContent = '2,384';
    document.getElementById('highSeverityEvents').textContent = '26';
}

function updateRecentEvents(events) {
    const recentEventsContainer = document.getElementById('recentEvents');
    
    if (!events || events.length === 0) {
        recentEventsContainer.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-inbox fa-3x mb-3"></i>
                <p>No events found</p>
            </div>
        `;
        return;
    }
    
    // Show last 10 events
    const recentEvents = events.slice(-10).reverse();
    
    const eventsHtml = recentEvents.map(event => `
        <div class="d-flex align-items-center mb-2 p-2 border-start border-3 ${getBorderClass(event.severity)}">
            <div class="flex-shrink-0 me-3">
                <span class="badge ${getSeverityBadgeClass(event.severity)}">${event.severity || 'Unknown'}</span>
            </div>
            <div class="flex-grow-1">
                <div class="fw-bold">${event.content}</div>
                <small class="text-muted">
                    <i class="fas fa-clock"></i> ${new Date(event.start).toLocaleString()}
                    <span class="ms-2">
                        <i class="fas fa-tag"></i> ${event.source || 'Unknown'}
                    </span>
                </small>
            </div>
        </div>
    `).join('');
    
    recentEventsContainer.innerHTML = eventsHtml;
}

function updateRecentEventsWithSampleData() {
    const sampleEvents = [
        { content: 'SSH successful login: admin from 192.168.1.10', start: new Date().toISOString(), severity: 'medium', source: 'linux_syslog' },
        { content: 'C2 command: execute whoami', start: new Date().toISOString(), severity: 'high', source: 'c2_log' },
        { content: 'Process creation: /bin/cat', start: new Date().toISOString(), severity: 'low', source: 'linux_syslog' }
    ];
    
    updateRecentEvents(sampleEvents);
}

function getBorderClass(severity) {
    switch(severity) {
        case 'high': return 'border-danger';
        case 'medium': return 'border-warning';
        case 'low': return 'border-secondary';
        default: return 'border-light';
    }
}

function getSeverityBadgeClass(severity) {
    switch(severity) {
        case 'high': return 'bg-danger';
        case 'medium': return 'bg-warning text-dark';
        case 'low': return 'bg-secondary';
        default: return 'bg-light text-dark';
    }
}

function isAnalysisInProgress() {
    const status = document.getElementById('analysisStatus').textContent;
    return status === 'processing' || status === 'uploading';
}

function refreshAnalysisStatus() {
    loadAnalysisData();
}

function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
}
</script>
{% endblock %}
