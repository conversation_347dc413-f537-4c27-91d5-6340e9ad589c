{% extends "base.html" %}

{% block title %}Upload Files - Forensic Timeline Analysis{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-upload"></i>
                        Upload Files for Forensic Timeline Analysis
                    </h3>
                    <p class="mb-0 mt-2">Upload your baseline and attack scenario files to create a comprehensive forensic timeline</p>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle"></i>
                        <strong>Flexible Analysis:</strong> Upload any combination of PCAP files and/or log files.
                        You can analyze network traffic only, system logs only, or both together for comprehensive forensic analysis.
                    </div>

                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="row">
                            <!-- Baseline Files Column -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-shield-alt"></i>
                                    Baseline Scenario Files <span class="badge bg-secondary">Optional</span>
                                </h5>
                                
                                <!-- Baseline PCAP Drop Zone -->
                                <div class="mb-4">
                                    <label class="form-label">Network Captures (PCAP) <span class="text-muted">- Optional</span></label>
                                    <div class="drop-zone" data-input="baseline_pcap" data-type="pcap" data-category="baseline">
                                        <div class="drop-zone-content">
                                            <i class="fas fa-network-wired fa-3x text-muted mb-3"></i>
                                            <p class="mb-2"><strong>Drop PCAP files here</strong></p>
                                            <p class="text-muted small">or click to browse</p>
                                            <p class="text-muted small">Supported: .pcap, .pcapng</p>
                                        </div>
                                        <input type="file" name="baseline_pcap" accept=".pcap,.pcapng" multiple style="display: none;">
                                    </div>
                                    <div class="file-list" id="baseline_pcap_list"></div>
                                </div>

                                <!-- Baseline Logs Drop Zone -->
                                <div class="mb-4">
                                    <label class="form-label">System Logs <span class="text-muted">- Optional</span></label>
                                    <div class="drop-zone" data-input="baseline_logs" data-type="logs" data-category="baseline">
                                        <div class="drop-zone-content">
                                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                            <p class="mb-2"><strong>Drop log files here</strong></p>
                                            <p class="text-muted small">or click to browse</p>
                                            <p class="text-muted small">Supported: .evtx, .log, .txt, .csv, .json</p>
                                        </div>
                                        <input type="file" name="baseline_logs" accept=".evtx,.log,.txt,.csv,.json" multiple style="display: none;">
                                    </div>
                                    <div class="file-list" id="baseline_logs_list"></div>
                                </div>
                            </div>

                            <!-- Attack Files Column -->
                            <div class="col-md-6">
                                <h5 class="text-danger mb-3">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Attack Scenario Files <span class="badge bg-secondary">Optional</span>
                                </h5>
                                
                                <!-- Attack PCAP Drop Zone -->
                                <div class="mb-4">
                                    <label class="form-label">Network Captures (PCAP) <span class="text-muted">- Optional</span></label>
                                    <div class="drop-zone" data-input="attack_pcap" data-type="pcap" data-category="attack">
                                        <div class="drop-zone-content">
                                            <i class="fas fa-network-wired fa-3x text-muted mb-3"></i>
                                            <p class="mb-2"><strong>Drop PCAP files here</strong></p>
                                            <p class="text-muted small">or click to browse</p>
                                            <p class="text-muted small">Supported: .pcap, .pcapng</p>
                                        </div>
                                        <input type="file" name="attack_pcap" accept=".pcap,.pcapng" multiple style="display: none;">
                                    </div>
                                    <div class="file-list" id="attack_pcap_list"></div>
                                </div>

                                <!-- Attack Logs Drop Zone -->
                                <div class="mb-4">
                                    <label class="form-label">System Logs <span class="text-muted">- Optional</span></label>
                                    <div class="drop-zone" data-input="attack_logs" data-type="logs" data-category="attack">
                                        <div class="drop-zone-content">
                                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                            <p class="mb-2"><strong>Drop log files here</strong></p>
                                            <p class="text-muted small">or click to browse</p>
                                            <p class="text-muted small">Supported: .evtx, .log, .txt, .csv, .json</p>
                                        </div>
                                        <input type="file" name="attack_logs" accept=".evtx,.log,.txt,.csv,.json" multiple style="display: none;">
                                    </div>
                                    <div class="file-list" id="attack_logs_list"></div>
                                </div>
                            </div>
                        </div>

                        <hr>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="uploadBtn" disabled>
                                <i class="fas fa-upload"></i>
                                Upload Files and Start Analysis
                            </button>
                        </div>
                    </form>

                    <!-- Progress Bar -->
                    <div id="uploadProgress" class="mt-4" style="display: none;">
                        <div class="progress mb-2">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="text-center">
                            <small id="uploadStatus" class="text-muted">Uploading files...</small>
                        </div>
                    </div>

                    <!-- Results -->
                    <div id="uploadResults" class="mt-4" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.drop-zone:hover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.drop-zone.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
    transform: scale(1.02);
}

.file-list {
    margin-top: 1rem;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

.file-item .file-info {
    display: flex;
    align-items: center;
}

.file-item .file-icon {
    margin-right: 0.5rem;
    color: #6c757d;
}

.file-item .file-name {
    font-weight: 500;
}

.file-item .file-size {
    color: #6c757d;
    font-size: 0.875rem;
    margin-left: 0.5rem;
}

.file-item .remove-file {
    color: #dc3545;
    cursor: pointer;
    padding: 0.25rem;
}

.file-item .remove-file:hover {
    color: #c82333;
}
</style>
{% endblock %}

{% block scripts %}
<script>
// File upload functionality
let uploadedFiles = new Map();

// Initialize drag and drop for all drop zones
document.querySelectorAll('.drop-zone').forEach(dropZone => {
    const input = dropZone.querySelector('input[type="file"]');
    const inputName = dropZone.dataset.input;
    
    // Click to browse
    dropZone.addEventListener('click', () => input.click());
    
    // Drag and drop events
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });
    
    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });
    
    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        handleFiles(e.dataTransfer.files, inputName, dropZone.dataset.type, dropZone.dataset.category);
    });
    
    // File input change
    input.addEventListener('change', (e) => {
        handleFiles(e.target.files, inputName, dropZone.dataset.type, dropZone.dataset.category);
    });
});

function handleFiles(files, inputName, fileType, category) {
    const fileList = document.getElementById(inputName + '_list');
    
    Array.from(files).forEach(file => {
        if (isValidFile(file, fileType)) {
            const fileId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            uploadedFiles.set(fileId, {
                file: file,
                inputName: inputName,
                type: fileType,
                category: category
            });
            
            addFileToList(fileList, file, fileId);
        }
    });
    
    updateUploadButton();
}

function isValidFile(file, fileType) {
    const allowedExtensions = {
        'pcap': ['.pcap', '.pcapng'],
        'logs': ['.evtx', '.log', '.txt', '.csv', '.json']
    };
    
    const ext = '.' + file.name.split('.').pop().toLowerCase();
    return allowedExtensions[fileType] && allowedExtensions[fileType].includes(ext);
}

function addFileToList(container, file, fileId) {
    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';
    fileItem.innerHTML = `
        <div class="file-info">
            <i class="fas fa-file file-icon"></i>
            <span class="file-name">${file.name}</span>
            <span class="file-size">(${formatFileSize(file.size)})</span>
        </div>
        <i class="fas fa-times remove-file" onclick="removeFile('${fileId}')"></i>
    `;
    container.appendChild(fileItem);
}

function removeFile(fileId) {
    uploadedFiles.delete(fileId);
    // Remove from DOM
    event.target.closest('.file-item').remove();
    updateUploadButton();
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function updateUploadButton() {
    const uploadBtn = document.getElementById('uploadBtn');
    uploadBtn.disabled = uploadedFiles.size === 0;
}

// Form submission
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    
    // Add all uploaded files to form data
    uploadedFiles.forEach((fileData, fileId) => {
        formData.append(fileData.inputName, fileData.file);
        formData.append(fileData.inputName + '_type', fileData.type);
        formData.append(fileData.inputName + '_category', fileData.category);
    });
    
    uploadFiles(formData);
});

function uploadFiles(formData) {
    const progressDiv = document.getElementById('uploadProgress');
    const resultsDiv = document.getElementById('uploadResults');
    const progressBar = progressDiv.querySelector('.progress-bar');
    const statusText = document.getElementById('uploadStatus');
    const uploadBtn = document.getElementById('uploadBtn');
    
    // Show progress bar
    progressDiv.style.display = 'block';
    resultsDiv.style.display = 'none';
    uploadBtn.disabled = true;
    progressBar.style.width = '0%';
    
    // Simulate progress
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
    }, 300);
    
    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        
        setTimeout(() => {
            progressDiv.style.display = 'none';
            uploadBtn.disabled = false;
            
            if (data.success) {
                resultsDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle"></i> Upload Successful!</h5>
                        <p>${data.message}</p>
                        <p><strong>Session ID:</strong> ${data.session_id}</p>
                        <div class="mt-3">
                            <a href="/analysis/${data.session_id}" class="btn btn-success me-2">
                                <i class="fas fa-chart-line"></i> View Analysis
                            </a>
                            <a href="/timeline/${data.session_id}" class="btn btn-info">
                                <i class="fas fa-clock"></i> View Timeline
                            </a>
                        </div>
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-circle"></i> Upload Failed</h5>
                        <p>${data.error}</p>
                    </div>
                `;
            }
            resultsDiv.style.display = 'block';
        }, 500);
    })
    .catch(error => {
        clearInterval(progressInterval);
        progressDiv.style.display = 'none';
        uploadBtn.disabled = false;
        resultsDiv.innerHTML = `
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle"></i> Upload Failed</h5>
                <p>Network error: ${error.message}</p>
            </div>
        `;
        resultsDiv.style.display = 'block';
    });
}
</script>
{% endblock %}
