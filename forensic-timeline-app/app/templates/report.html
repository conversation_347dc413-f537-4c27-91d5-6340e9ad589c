{% extends "base.html" %}

{% block title %}Analysis Report - Forensic Timeline Analysis{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h3 class="mb-0">
                                <i class="fas fa-file-alt"></i>
                                Forensic Analysis Report
                            </h3>
                            <small>Session: {{ session_id }}</small>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="generateReport('html')">
                                    <i class="fas fa-file-alt"></i> HTML Report
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="generateReport('json')">
                                    <i class="fas fa-file"></i> JSON Report
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="generateReport('markdown')">
                                    <i class="fas fa-file-alt"></i> Markdown Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Report Generation Status -->
                    <div id="reportStatus" class="alert alert-info" style="display: none;">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                            <span>Generating report...</span>
                        </div>
                    </div>

                    <!-- Report Content -->
                    <div id="reportContent">
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Report Generated</h4>
                            <p class="text-muted">Click one of the buttons above to generate a comprehensive forensic analysis report.</p>
                        </div>
                    </div>

                    <!-- Report Preview -->
                    <div id="reportPreview" style="display: none;">
                        <div class="row">
                            <div class="col-md-12">
                                <h5>Report Preview</h5>
                                <div class="border rounded p-3 bg-light">
                                    <iframe id="reportFrame" width="100%" height="600" frameborder="0"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Download Links -->
                    <div id="downloadLinks" style="display: none;">
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h5>Download Report</h5>
                                <div class="list-group">
                                    <a id="downloadLink" href="#" class="list-group-item list-group-item-action" download>
                                        <i class="fas fa-download"></i> Download Generated Report
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('main.analysis_page', session_id=session_id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Analysis
                </a>
                <a href="{{ url_for('main.timeline_page', session_id=session_id) }}" class="btn btn-primary">
                    <i class="fas fa-clock"></i> View Timeline
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Report Generation Modal -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Report Generation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="modalContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status"></div>
                        <p class="mt-2">Generating comprehensive forensic analysis report...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="downloadReportBtn" style="display: none;">
                    <i class="fas fa-download"></i> Download Report
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentSessionId = '{{ session_id }}';
let reportModal;

document.addEventListener('DOMContentLoaded', function() {
    reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
});

function generateReport(format) {
    console.log(`Generating ${format} report for session ${currentSessionId}`);
    
    // Show status
    const statusDiv = document.getElementById('reportStatus');
    statusDiv.style.display = 'block';
    statusDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
            <span>Generating ${format.toUpperCase()} report...</span>
        </div>
    `;
    
    // Hide previous content
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('reportPreview').style.display = 'none';
    document.getElementById('downloadLinks').style.display = 'none';
    
    // Make API call to generate report
    fetch(`/api/report/${currentSessionId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            format: format
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Report generated:', data);
        
        // Hide status
        statusDiv.style.display = 'none';
        
        if (data.success) {
            showReportSuccess(data, format);
        } else {
            showReportError(data.error || 'Unknown error occurred');
        }
    })
    .catch(error => {
        console.error('Report generation failed:', error);
        statusDiv.style.display = 'none';
        showReportError(error.message);
    });
}

function showReportSuccess(data, format) {
    const contentDiv = document.getElementById('reportContent');
    const previewDiv = document.getElementById('reportPreview');
    const downloadDiv = document.getElementById('downloadLinks');
    
    // Show success message
    contentDiv.innerHTML = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <strong>Report Generated Successfully!</strong>
            <p class="mb-0">Your ${format.toUpperCase()} report has been generated and is ready for download.</p>
        </div>
        <div class="row">
            <div class="col-md-6">
                <h6>Report Details</h6>
                <ul class="list-unstyled">
                    <li><strong>Format:</strong> ${format.toUpperCase()}</li>
                    <li><strong>Size:</strong> ${data.size || 'Unknown'}</li>
                    <li><strong>Generated:</strong> ${new Date().toLocaleString()}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Report Summary</h6>
                <ul class="list-unstyled">
                    <li><strong>Events Analyzed:</strong> ${data.summary?.total_events || 'N/A'}</li>
                    <li><strong>Correlations Found:</strong> ${data.summary?.correlations || 'N/A'}</li>
                    <li><strong>Threat Level:</strong> ${data.summary?.threat_level || 'N/A'}</li>
                </ul>
            </div>
        </div>
    `;
    contentDiv.style.display = 'block';
    
    // Show download link
    if (data.download_url) {
        const downloadLink = document.getElementById('downloadLink');
        downloadLink.href = data.download_url;
        downloadLink.innerHTML = `<i class="fas fa-download"></i> Download ${format.toUpperCase()} Report`;
        downloadDiv.style.display = 'block';
    }
    
    // Show preview for HTML reports
    if (format === 'html' && data.preview_url) {
        const reportFrame = document.getElementById('reportFrame');
        reportFrame.src = data.preview_url;
        previewDiv.style.display = 'block';
    }
}

function showReportError(errorMessage) {
    const contentDiv = document.getElementById('reportContent');
    contentDiv.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <strong>Report Generation Failed</strong>
            <p class="mb-0">${errorMessage}</p>
        </div>
        <div class="text-center py-3">
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="fas fa-redo"></i> Try Again
            </button>
        </div>
    `;
    contentDiv.style.display = 'block';
}
</script>
{% endblock %}
