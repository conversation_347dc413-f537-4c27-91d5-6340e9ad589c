{% extends "base.html" %}

{% block title %}Home - Forensic Timeline Analysis{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <!-- Hero Section -->
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold text-primary">Forensic Timeline Analysis</h1>
            <p class="lead">Correlate network traffic captures and system logs across baseline and attack scenarios</p>
        </div>

        <!-- Features Grid -->
        <div class="row g-4 mb-5">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-network-wired fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Network Analysis</h5>
                        <p class="card-text">Integrated Suricata IDS and Zeek for comprehensive offline PCAP analysis</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-file-alt fa-3x text-success mb-3"></i>
                        <h5 class="card-title">Log Parsing</h5>
                        <p class="card-text">Support for Windows EVTX, Linux syslogs, and custom C2 logs</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                        <h5 class="card-title">Timeline Correlation</h5>
                        <p class="card-text">Multi-source event correlation with interactive visualization</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-balance-scale fa-3x text-info mb-3"></i>
                        <h5 class="card-title">Baseline Comparison</h5>
                        <p class="card-text">Highlight differences between normal and attack scenarios</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Start -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-rocket"></i> Quick Start</h5>
            </div>
            <div class="card-body">
                <ol class="list-group list-group-numbered list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">Upload Files</div>
                            Upload your baseline and attack PCAPs along with corresponding logs
                        </div>
                        <a href="{{ url_for('main.upload_files') }}" class="btn btn-primary btn-sm">Upload</a>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">Start Analysis</div>
                            Automated processing with Suricata and Zeek network analysis
                        </div>
                        <span class="badge bg-secondary rounded-pill">Auto</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">View Timeline</div>
                            Interactive timeline visualization with event correlation
                        </div>
                        <span class="badge bg-success rounded-pill">Interactive</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">Generate Report</div>
                            Comprehensive forensic report with findings and recommendations
                        </div>
                        <span class="badge bg-info rounded-pill">Export</span>
                    </li>
                </ol>
            </div>
        </div>

        <!-- API Information -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-code"></i> API Access</h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text small">Programmatic access via REST API</p>
                        <a href="/api/health" class="btn btn-outline-primary btn-sm" target="_blank">API Status</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt"></i> Security</h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text small">Offline analysis • Container isolation • Local processing</p>
                        <span class="badge bg-success">Secure</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}