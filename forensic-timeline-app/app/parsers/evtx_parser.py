"""
Windows EVTX log parser for forensic timeline analysis.
"""

import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
import xml.etree.ElementTree as ET

from ..models.event import Event, EventType, EventSource, EventCategory

logger = logging.getLogger(__name__)

class EvtxParser:
    """
    Parser for Windows Event Log (EVTX) files.

    This parser extracts security-relevant events from Windows EVTX files
    and converts them to our standardized Event format for timeline analysis.
    """

    def __init__(self):
        # Priority event IDs for forensic analysis
        self.priority_event_ids = {
            # Authentication events
            4624: "Successful logon",
            4625: "Failed logon",
            4634: "Account logoff",
            4648: "Logon using explicit credentials",
            4672: "Special privileges assigned",
            4720: "User account created",
            4726: "User account deleted",
            4740: "User account locked",
            4767: "User account unlocked",

            # Process events
            4688: "Process creation",
            4689: "Process termination",

            # Object access
            4656: "Handle to object requested",
            4658: "Handle to object closed",
            4663: "Object access attempted",

            # Policy changes
            4719: "System audit policy changed",
            4739: "Domain policy changed",

            # System events
            4608: "Windows starting up",
            4609: "Windows shutting down",
            4616: "System time changed",

            # Service events
            7034: "Service crashed",
            7035: "Service control manager",
            7036: "Service started/stopped",
            7040: "Service startup type changed",

            # Security events
            4697: "Service installed",
            4698: "Scheduled task created",
            4699: "Scheduled task deleted",
            4700: "Scheduled task enabled",
            4701: "Scheduled task disabled",
            4702: "Scheduled task updated"
        }

    def parse_evtx_file(self, evtx_path: str, category: EventCategory) -> List[Event]:
        """
        Parse a Windows EVTX file and extract events.

        Args:
            evtx_path: Path to the EVTX file
            category: Event category (baseline/attack)

        Returns:
            List of Event objects
        """
        if not os.path.exists(evtx_path):
            raise FileNotFoundError(f"EVTX file not found: {evtx_path}")

        logger.info(f"Parsing EVTX file: {evtx_path}")
        events = []

        try:
            # Try to use python-evtx library if available
            events = self._parse_with_python_evtx(evtx_path, category)
        except ImportError:
            logger.warning("python-evtx library not available, falling back to XML parsing")
            # Fallback to basic XML parsing (limited functionality)
            events = self._parse_with_xml_fallback(evtx_path, category)
        except Exception as e:
            logger.error(f"Failed to parse EVTX file {evtx_path}: {e}")
            raise

        logger.info(f"Extracted {len(events)} events from {evtx_path}")
        return events

    def _parse_with_python_evtx(self, evtx_path: str, category: EventCategory) -> List[Event]:
        """Parse EVTX using python-evtx library (preferred method)."""
        try:
            import Evtx.Evtx as evtx
            import Evtx.Views as e_views
        except ImportError:
            raise ImportError("python-evtx library not installed")

        events = []

        with evtx.Evtx(evtx_path) as log:
            for record in log.records():
                try:
                    event = self._convert_evtx_record(record, category)
                    if event:
                        events.append(event)
                except Exception as e:
                    logger.debug(f"Failed to parse EVTX record: {e}")
                    continue

        return events

    def _parse_with_xml_fallback(self, evtx_path: str, category: EventCategory) -> List[Event]:
        """
        Fallback XML parsing method (limited functionality).

        Note: This is a basic fallback that won't work with binary EVTX files.
        It's mainly here for demonstration and would need a proper EVTX library.
        """
        logger.warning("Using XML fallback parser - limited EVTX support")

        # This is a placeholder - real EVTX files are binary and need proper parsing
        # In production, you'd need python-evtx or similar library
        events = []

        try:
            # Try to parse as XML (won't work with real EVTX files)
            tree = ET.parse(evtx_path)
            root = tree.getroot()

            for event_elem in root.findall('.//Event'):
                try:
                    event = self._convert_xml_event(event_elem, category)
                    if event:
                        events.append(event)
                except Exception as e:
                    logger.debug(f"Failed to parse XML event: {e}")
                    continue

        except ET.ParseError:
            logger.error("File is not valid XML - binary EVTX files require python-evtx library")
        except Exception as e:
            logger.error(f"XML parsing failed: {e}")

        return events

    def _convert_evtx_record(self, record, category: EventCategory) -> Optional[Event]:
        """Convert an EVTX record to our Event format."""
        try:
            # Parse the XML from the record
            xml_string = record.xml()
            root = ET.fromstring(xml_string)

            # Extract basic event information
            system = root.find('.//{http://schemas.microsoft.com/win/2004/08/events/event}System')
            if system is None:
                return None

            # Get event ID
            event_id_elem = system.find('.//{http://schemas.microsoft.com/win/2004/08/events/event}EventID')
            if event_id_elem is None:
                return None

            event_id = int(event_id_elem.text)

            # Skip events we're not interested in
            if event_id not in self.priority_event_ids:
                return None

            # Get timestamp
            time_created = system.find('.//{http://schemas.microsoft.com/win/2004/08/events/event}TimeCreated')
            if time_created is None:
                return None

            timestamp_str = time_created.get('SystemTime')
            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))

            # Get other system information
            computer = system.find('.//{http://schemas.microsoft.com/win/2004/08/events/event}Computer')
            computer_name = computer.text if computer is not None else "Unknown"

            level_elem = system.find('.//{http://schemas.microsoft.com/win/2004/08/events/event}Level')
            level = int(level_elem.text) if level_elem is not None else 0

            # Get event data
            event_data = root.find('.//{http://schemas.microsoft.com/win/2004/08/events/event}EventData')
            metadata = self._extract_event_data(event_data, event_id)

            # Add system metadata
            metadata.update({
                'event_id': event_id,
                'computer': computer_name,
                'level': level,
                'record_id': record.record_num()
            })

            # Determine event type and description
            event_type = self._map_event_id_to_type(event_id)
            description = self._build_event_description(event_id, metadata)
            severity = self._determine_severity(event_id, level)

            event = Event(
                timestamp=timestamp,
                source=EventSource.WINDOWS_EVTX,
                event_type=event_type,
                category=category,
                description=description,
                severity=severity,
                metadata=metadata
            )

            return event

        except Exception as e:
            logger.debug(f"Failed to convert EVTX record: {e}")
            return None

    def _convert_xml_event(self, event_elem, category: EventCategory) -> Optional[Event]:
        """Convert XML event element to our Event format (fallback method)."""
        try:
            # This is a simplified version for XML fallback
            # Real implementation would need proper EVTX parsing

            system = event_elem.find('.//System')
            if system is None:
                return None

            event_id_elem = system.find('.//EventID')
            if event_id_elem is None:
                return None

            event_id = int(event_id_elem.text)

            # Skip events we're not interested in
            if event_id not in self.priority_event_ids:
                return None

            # Get timestamp
            time_created = system.find('.//TimeCreated')
            if time_created is None:
                return None

            timestamp_str = time_created.get('SystemTime')
            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))

            # Basic metadata
            metadata = {'event_id': event_id}

            # Determine event type and description
            event_type = self._map_event_id_to_type(event_id)
            description = self.priority_event_ids.get(event_id, f"Windows Event {event_id}")

            event = Event(
                timestamp=timestamp,
                source=EventSource.WINDOWS_EVTX,
                event_type=event_type,
                category=category,
                description=description,
                metadata=metadata
            )

            return event

        except Exception as e:
            logger.debug(f"Failed to convert XML event: {e}")
            return None

    def _extract_event_data(self, event_data_elem, event_id: int) -> Dict[str, Any]:
        """Extract event-specific data from EventData element."""
        metadata = {}

        if event_data_elem is None:
            return metadata

        # Extract all data elements
        for data_elem in event_data_elem.findall('.//{http://schemas.microsoft.com/win/2004/08/events/event}Data'):
            name = data_elem.get('Name', '')
            value = data_elem.text or ''

            if name:
                metadata[name] = value

        # Event-specific parsing
        if event_id in [4624, 4625]:  # Logon events
            metadata.update({
                'logon_type': metadata.get('LogonType', ''),
                'account_name': metadata.get('TargetUserName', ''),
                'account_domain': metadata.get('TargetDomainName', ''),
                'source_ip': metadata.get('IpAddress', ''),
                'workstation': metadata.get('WorkstationName', '')
            })
        elif event_id == 4688:  # Process creation
            metadata.update({
                'process_name': metadata.get('NewProcessName', ''),
                'process_id': metadata.get('NewProcessId', ''),
                'parent_process_name': metadata.get('ParentProcessName', ''),
                'command_line': metadata.get('CommandLine', ''),
                'creator_process_id': metadata.get('CreatorProcessId', '')
            })
        elif event_id in [4720, 4726]:  # Account creation/deletion
            metadata.update({
                'target_account': metadata.get('TargetUserName', ''),
                'target_domain': metadata.get('TargetDomainName', ''),
                'subject_account': metadata.get('SubjectUserName', ''),
                'subject_domain': metadata.get('SubjectDomainName', '')
            })

        return metadata

    def _map_event_id_to_type(self, event_id: int) -> EventType:
        """Map Windows event ID to our EventType enum."""
        if event_id in [4624, 4625, 4634, 4648, 4672, 4720, 4726, 4740, 4767]:
            return EventType.AUTHENTICATION
        elif event_id in [4688, 4689]:
            return EventType.PROCESS_CREATION
        elif event_id in [4697, 4698, 4699, 4700, 4701, 4702]:
            return EventType.SYSTEM_LOG
        else:
            return EventType.SYSTEM_LOG

    def _build_event_description(self, event_id: int, metadata: Dict[str, Any]) -> str:
        """Build a descriptive string for the event."""
        base_description = self.priority_event_ids.get(event_id, f"Windows Event {event_id}")

        # Add context based on event type
        if event_id in [4624, 4625]:  # Logon events
            account = metadata.get('account_name', 'Unknown')
            source_ip = metadata.get('source_ip', '')
            if source_ip and source_ip != '-':
                return f"{base_description}: {account} from {source_ip}"
            else:
                return f"{base_description}: {account}"

        elif event_id == 4688:  # Process creation
            process_name = metadata.get('process_name', 'Unknown')
            return f"{base_description}: {process_name}"

        elif event_id in [4720, 4726]:  # Account creation/deletion
            target_account = metadata.get('target_account', 'Unknown')
            return f"{base_description}: {target_account}"

        else:
            return base_description

    def _determine_severity(self, event_id: int, level: int) -> Optional[str]:
        """Determine event severity based on event ID and level."""
        # High severity events
        if event_id in [4625, 4740, 4697, 7034]:  # Failed logon, account locked, service installed, service crashed
            return "high"

        # Medium severity events
        elif event_id in [4624, 4720, 4726, 4688]:  # Successful logon, account created/deleted, process creation
            return "medium"

        # Low severity events
        elif event_id in [4634, 7036]:  # Logoff, service start/stop
            return "low"

        # Use Windows event level as fallback
        elif level <= 2:  # Critical/Error
            return "high"
        elif level == 3:  # Warning
            return "medium"
        else:  # Information
            return "low"