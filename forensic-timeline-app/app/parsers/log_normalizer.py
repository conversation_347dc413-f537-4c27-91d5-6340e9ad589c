"""
Log normalization engine for forensic timeline analysis.
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
import pytz

from ..models.event import Event, EventCategory
from .evtx_parser import EvtxParser
from .syslog_parser import SyslogParser
from .c2_log_parser import C2LogParser

logger = logging.getLogger(__name__)

class LogNormalizer:
    """
    Log normalization engine that coordinates multiple parsers and
    normalizes events from different sources into a common format.
    """

    def __init__(self):
        self.evtx_parser = EvtxParser()
        self.syslog_parser = SyslogParser()
        self.c2_parser = C2LogParser()

        # File extension to parser mapping
        self.parser_mapping = {
            '.evtx': self.evtx_parser.parse_evtx_file,
            '.log': self.syslog_parser.parse_syslog_file,
            '.txt': self._parse_text_file,
            '.csv': self.c2_parser.parse_c2_log_file,
            '.json': self.c2_parser.parse_c2_log_file,
        }

    def parse_log_files(self, log_files: List[Dict[str, Any]], category: EventCategory) -> List[Event]:
        """
        Parse multiple log files and return normalized events.

        Args:
            log_files: List of file dictionaries with 'filepath' and optional 'file_type'
            category: Event category (baseline/attack)

        Returns:
            List of normalized Event objects
        """
        all_events = []

        for file_info in log_files:
            filepath = file_info['filepath']
            file_type = file_info.get('file_type', 'auto')

            try:
                events = self.parse_single_log_file(filepath, category, file_type)
                all_events.extend(events)
                logger.info(f"Parsed {len(events)} events from {filepath}")

            except Exception as e:
                logger.error(f"Failed to parse log file {filepath}: {e}")
                continue

        # Normalize and validate all events
        normalized_events = self._normalize_events(all_events)

        logger.info(f"Total normalized events: {len(normalized_events)}")
        return normalized_events

    def parse_single_log_file(self, filepath: str, category: EventCategory,
                            file_type: str = 'auto') -> List[Event]:
        """
        Parse a single log file using the appropriate parser.

        Args:
            filepath: Path to the log file
            category: Event category (baseline/attack)
            file_type: File type hint ('auto', 'evtx', 'syslog', 'c2', etc.)

        Returns:
            List of Event objects
        """
        if file_type == 'auto':
            file_type = self._detect_file_type(filepath)

        # Select appropriate parser
        if file_type == 'evtx':
            return self.evtx_parser.parse_evtx_file(filepath, category)
        elif file_type == 'syslog':
            return self.syslog_parser.parse_syslog_file(filepath, category)
        elif file_type == 'c2':
            return self.c2_parser.parse_c2_log_file(filepath, category)
        else:
            # Try to determine based on file extension
            extension = self._get_file_extension(filepath)
            if extension in self.parser_mapping:
                parser_func = self.parser_mapping[extension]
                if extension in ['.csv', '.json']:
                    return parser_func(filepath, category)
                else:
                    return parser_func(filepath, category)
            else:
                # Default to syslog parser for unknown text files
                return self.syslog_parser.parse_syslog_file(filepath, category)

    def _detect_file_type(self, filepath: str) -> str:
        """Detect file type based on extension and content."""
        extension = self._get_file_extension(filepath)

        # Direct mapping for known extensions
        if extension == '.evtx':
            return 'evtx'
        elif extension in ['.csv', '.json']:
            return 'c2'
        else:
            # For .log and .txt files, try to detect content type
            try:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    sample = f.read(1024)

                    # Check for C2 indicators
                    c2_indicators = ['command', 'agent', 'beacon', 'execute', 'payload']
                    if any(indicator in sample.lower() for indicator in c2_indicators):
                        return 'c2'

                    # Check for Windows event log indicators
                    windows_indicators = ['eventid', 'logname', 'computer']
                    if any(indicator in sample.lower() for indicator in windows_indicators):
                        return 'evtx'  # Might be exported Windows events

                    # Default to syslog
                    return 'syslog'

            except Exception:
                return 'syslog'  # Safe default

    def _get_file_extension(self, filepath: str) -> str:
        """Get file extension in lowercase."""
        import os
        return os.path.splitext(filepath)[1].lower()

    def _parse_text_file(self, filepath: str, category: EventCategory) -> List[Event]:
        """Parse generic text file - try to determine format."""
        # First try C2 parser
        try:
            events = self.c2_parser.parse_c2_log_file(filepath, category, 'text')
            if events:
                return events
        except Exception:
            pass

        # Fall back to syslog parser
        return self.syslog_parser.parse_syslog_file(filepath, category)

    def _normalize_events(self, events: List[Event]) -> List[Event]:
        """
        Normalize events to ensure consistency.

        This includes:
        - UTC timestamp conversion
        - Data validation
        - Duplicate detection
        - Metadata standardization
        """
        normalized_events = []
        seen_events = set()

        for event in events:
            try:
                # Normalize timestamp to UTC
                if event.timestamp.tzinfo is None:
                    # Assume local timezone if no timezone info
                    event.timestamp = event.timestamp.replace(tzinfo=pytz.UTC)
                elif event.timestamp.tzinfo != pytz.UTC:
                    # Convert to UTC
                    event.timestamp = event.timestamp.astimezone(pytz.UTC)

                # Validate required fields
                if not self._validate_event(event):
                    logger.debug(f"Skipping invalid event: {event.event_id}")
                    continue

                # Check for duplicates (basic deduplication)
                event_signature = self._generate_event_signature(event)
                if event_signature in seen_events:
                    logger.debug(f"Skipping duplicate event: {event.event_id}")
                    continue

                seen_events.add(event_signature)

                # Standardize metadata
                event.metadata = self._standardize_metadata(event.metadata)

                normalized_events.append(event)

            except Exception as e:
                logger.warning(f"Failed to normalize event {event.event_id}: {e}")
                continue

        # Sort by timestamp
        normalized_events.sort(key=lambda e: e.timestamp)

        return normalized_events

    def _validate_event(self, event: Event) -> bool:
        """Validate that an event has required fields."""
        try:
            # Check required fields
            if not event.timestamp:
                return False
            if not event.source:
                return False
            if not event.event_type:
                return False
            if not event.category:
                return False
            if not event.description:
                return False

            # Check timestamp is reasonable (not too far in future/past)
            now = datetime.now(pytz.UTC)
            time_diff = abs((event.timestamp - now).total_seconds())

            # Allow events up to 10 years in the past or 1 day in the future
            if time_diff > (10 * 365 * 24 * 3600) or (event.timestamp > now and time_diff > 24 * 3600):
                logger.debug(f"Event timestamp out of reasonable range: {event.timestamp}")
                return False

            return True

        except Exception as e:
            logger.debug(f"Event validation failed: {e}")
            return False

    def _generate_event_signature(self, event: Event) -> str:
        """Generate a signature for duplicate detection."""
        # Create a signature based on key fields
        signature_parts = [
            str(event.timestamp),
            event.source.value,
            event.event_type.value,
            event.description[:100],  # First 100 chars of description
        ]

        # Add some metadata for uniqueness
        if 'src_ip' in event.metadata:
            signature_parts.append(str(event.metadata['src_ip']))
        if 'dst_ip' in event.metadata:
            signature_parts.append(str(event.metadata['dst_ip']))
        if 'process_name' in event.metadata:
            signature_parts.append(str(event.metadata['process_name']))

        return '|'.join(signature_parts)

    def _standardize_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Standardize metadata field names and values."""
        standardized = {}

        # Field name mappings for consistency
        field_mappings = {
            # IP addresses
            'src_ip': ['source_ip', 'orig_h', 'client_ip'],
            'dst_ip': ['destination_ip', 'dest_ip', 'resp_h', 'server_ip'],

            # Ports
            'src_port': ['source_port', 'orig_p', 'client_port'],
            'dst_port': ['destination_port', 'dest_port', 'resp_p', 'server_port'],

            # Process information
            'process_name': ['proc_name', 'executable', 'image'],
            'process_id': ['proc_id', 'pid'],
            'parent_process': ['parent_proc', 'parent_image'],

            # User information
            'username': ['user', 'account', 'login'],
            'domain': ['user_domain', 'account_domain'],

            # Network information
            'protocol': ['proto', 'ip_proto'],
            'hostname': ['host', 'computer', 'machine'],
        }

        # Apply field mappings
        for standard_field, aliases in field_mappings.items():
            # Check if standard field already exists
            if standard_field in metadata:
                standardized[standard_field] = metadata[standard_field]
            else:
                # Look for aliases
                for alias in aliases:
                    if alias in metadata:
                        standardized[standard_field] = metadata[alias]
                        break

        # Copy remaining fields that don't have mappings
        for key, value in metadata.items():
            if key not in standardized and not any(key in aliases for aliases in field_mappings.values()):
                standardized[key] = value

        # Standardize values
        for key, value in standardized.items():
            if isinstance(value, str):
                # Clean up string values
                value = value.strip()

                # Standardize common values
                if key in ['protocol', 'proto']:
                    value = value.upper()
                elif key in ['username', 'user', 'account']:
                    value = value.lower()
                elif key in ['hostname', 'computer']:
                    value = value.lower()

                standardized[key] = value

        return standardized