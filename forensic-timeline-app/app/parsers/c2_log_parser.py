"""
Command and Control (C2) log parser for forensic timeline analysis.
"""

import os
import re
import json
import csv
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
import pytz

from ..models.event import Event, EventType, EventSource, EventCategory

logger = logging.getLogger(__name__)

class C2LogParser:
    """
    Parser for Command and Control (C2) server logs.

    This parser handles various C2 log formats including CSV, JSON, and
    plain text logs with configurable timestamp patterns.
    """

    def __init__(self):
        # Common timestamp patterns for C2 logs
        self.timestamp_patterns = [
            # ISO format: "2023-01-15 14:30:25"
            (r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', '%Y-%m-%d %H:%M:%S'),

            # RFC3339: "2023-01-15T14:30:25Z"
            (r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z?)', '%Y-%m-%dT%H:%M:%S'),

            # Unix timestamp
            (r'(\d{10}(?:\.\d{3})?)', 'unix'),

            # US format: "01/15/2023 14:30:25"
            (r'(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2})', '%m/%d/%Y %H:%M:%S'),

            # European format: "15/01/2023 14:30:25"
            (r'(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2})', '%d/%m/%Y %H:%M:%S'),

            # Log format: "[2023-01-15 14:30:25]"
            (r'\[(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\]', '%Y-%m-%d %H:%M:%S'),
        ]

        # Compile regex patterns
        self.compiled_patterns = [(re.compile(pattern), fmt) for pattern, fmt in self.timestamp_patterns]

        # C2 command patterns
        self.c2_patterns = {
            'beacon': re.compile(r'(?:beacon|checkin|heartbeat)', re.IGNORECASE),
            'command': re.compile(r'(?:execute|exec|cmd|run|shell)', re.IGNORECASE),
            'download': re.compile(r'(?:download|get|fetch|retrieve)', re.IGNORECASE),
            'upload': re.compile(r'(?:upload|put|send|transfer)', re.IGNORECASE),
            'persistence': re.compile(r'(?:persist|install|startup|autorun)', re.IGNORECASE),
            'lateral_movement': re.compile(r'(?:lateral|pivot|jump|spread)', re.IGNORECASE),
            'exfiltration': re.compile(r'(?:exfil|steal|copy|backup)', re.IGNORECASE),
            'reconnaissance': re.compile(r'(?:recon|scan|enum|discover)', re.IGNORECASE),
            'privilege_escalation': re.compile(r'(?:elevate|escalate|admin|root)', re.IGNORECASE),
        }

    def parse_c2_log_file(self, log_path: str, category: EventCategory,
                         log_format: str = 'auto') -> List[Event]:
        """
        Parse a C2 log file and extract events.

        Args:
            log_path: Path to the C2 log file
            category: Event category (baseline/attack)
            log_format: Format hint ('auto', 'json', 'csv', 'text')

        Returns:
            List of Event objects
        """
        if not os.path.exists(log_path):
            raise FileNotFoundError(f"C2 log file not found: {log_path}")

        logger.info(f"Parsing C2 log file: {log_path}")

        # Auto-detect format if not specified
        if log_format == 'auto':
            log_format = self._detect_format(log_path)

        events = []

        try:
            if log_format == 'json':
                events = self._parse_json_log(log_path, category)
            elif log_format == 'csv':
                events = self._parse_csv_log(log_path, category)
            else:  # text format
                events = self._parse_text_log(log_path, category)

        except Exception as e:
            logger.error(f"Failed to parse C2 log file {log_path}: {e}")
            raise

        logger.info(f"Extracted {len(events)} events from {log_path}")
        return events

    def _detect_format(self, log_path: str) -> str:
        """Auto-detect the log file format."""
        try:
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                first_line = f.readline().strip()

                # Check for JSON
                if first_line.startswith('{') and first_line.endswith('}'):
                    try:
                        json.loads(first_line)
                        return 'json'
                    except json.JSONDecodeError:
                        pass

                # Check for CSV (look for commas and potential headers)
                if ',' in first_line and len(first_line.split(',')) > 2:
                    return 'csv'

                # Default to text
                return 'text'

        except Exception:
            return 'text'

    def _parse_json_log(self, log_path: str, category: EventCategory) -> List[Event]:
        """Parse JSON format C2 logs."""
        events = []

        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    log_entry = json.loads(line)
                    event = self._convert_json_entry(log_entry, category)
                    if event:
                        events.append(event)
                except json.JSONDecodeError as e:
                    logger.debug(f"Failed to parse JSON line {line_num}: {e}")
                    continue
                except Exception as e:
                    logger.debug(f"Failed to convert JSON entry on line {line_num}: {e}")
                    continue

        return events

    def _parse_csv_log(self, log_path: str, category: EventCategory) -> List[Event]:
        """Parse CSV format C2 logs."""
        events = []

        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            # Try to detect delimiter
            sample = f.read(1024)
            f.seek(0)

            delimiter = ','
            if ';' in sample and sample.count(';') > sample.count(','):
                delimiter = ';'
            elif '\t' in sample:
                delimiter = '\t'

            reader = csv.DictReader(f, delimiter=delimiter)

            for row_num, row in enumerate(reader, 1):
                try:
                    event = self._convert_csv_row(row, category)
                    if event:
                        events.append(event)
                except Exception as e:
                    logger.debug(f"Failed to convert CSV row {row_num}: {e}")
                    continue

        return events

    def _parse_text_log(self, log_path: str, category: EventCategory) -> List[Event]:
        """Parse plain text format C2 logs."""
        events = []

        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    event = self._parse_text_line(line, category)
                    if event:
                        events.append(event)
                except Exception as e:
                    logger.debug(f"Failed to parse text line {line_num}: {e}")
                    continue

        return events

    def _convert_json_entry(self, log_entry: Dict[str, Any], category: EventCategory) -> Optional[Event]:
        """Convert a JSON log entry to an Event object."""
        try:
            # Extract timestamp
            timestamp = self._extract_timestamp_from_dict(log_entry)
            if not timestamp:
                return None

            # Extract command/action information
            command = log_entry.get('command', log_entry.get('action', ''))
            agent_id = log_entry.get('agent_id', log_entry.get('client_id', ''))
            target = log_entry.get('target', log_entry.get('host', ''))

            # Determine C2 activity type
            activity_type = self._classify_c2_activity(command)

            # Build description
            description = f"C2 {activity_type}: {command}"
            if agent_id:
                description += f" (Agent: {agent_id})"
            if target:
                description += f" (Target: {target})"

            # Build metadata
            metadata = {
                'command': command,
                'agent_id': agent_id,
                'target': target,
                'activity_type': activity_type,
                'raw_entry': log_entry
            }

            # Add any additional fields from the log entry
            for key, value in log_entry.items():
                if key not in ['timestamp', 'time', 'command', 'action', 'agent_id', 'client_id']:
                    metadata[key] = value

            return Event(
                timestamp=timestamp,
                source=EventSource.C2_LOG,
                event_type=EventType.C2_COMMAND,
                category=category,
                description=description,
                severity=self._determine_c2_severity(activity_type),
                metadata=metadata
            )

        except Exception as e:
            logger.debug(f"Failed to convert JSON entry: {e}")
            return None

    def _convert_csv_row(self, row: Dict[str, str], category: EventCategory) -> Optional[Event]:
        """Convert a CSV row to an Event object."""
        try:
            # Extract timestamp from various possible column names
            timestamp = None
            for time_col in ['timestamp', 'time', 'datetime', 'date']:
                if time_col in row and row[time_col]:
                    timestamp = self._parse_timestamp_string(row[time_col])
                    break

            if not timestamp:
                return None

            # Extract command/action information
            command = ''
            for cmd_col in ['command', 'action', 'cmd', 'operation']:
                if cmd_col in row and row[cmd_col]:
                    command = row[cmd_col]
                    break

            if not command:
                return None

            # Extract other information
            agent_id = row.get('agent_id', row.get('client_id', ''))
            target = row.get('target', row.get('host', ''))

            # Determine C2 activity type
            activity_type = self._classify_c2_activity(command)

            # Build description
            description = f"C2 {activity_type}: {command}"
            if agent_id:
                description += f" (Agent: {agent_id})"
            if target:
                description += f" (Target: {target})"

            # Build metadata
            metadata = {
                'command': command,
                'agent_id': agent_id,
                'target': target,
                'activity_type': activity_type,
                'raw_row': dict(row)
            }

            return Event(
                timestamp=timestamp,
                source=EventSource.C2_LOG,
                event_type=EventType.C2_COMMAND,
                category=category,
                description=description,
                severity=self._determine_c2_severity(activity_type),
                metadata=metadata
            )

        except Exception as e:
            logger.debug(f"Failed to convert CSV row: {e}")
            return None

    def _parse_text_line(self, line: str, category: EventCategory) -> Optional[Event]:
        """Parse a plain text log line."""
        try:
            # Extract timestamp
            timestamp = self._parse_timestamp_from_line(line)
            if not timestamp:
                return None

            # Look for C2 activity patterns
            activity_type = None
            for pattern_name, pattern in self.c2_patterns.items():
                if pattern.search(line):
                    activity_type = pattern_name
                    break

            if not activity_type:
                # If no specific pattern matches, treat as generic C2 activity
                activity_type = 'unknown'

            # Extract command if possible
            command = self._extract_command_from_line(line)

            description = f"C2 {activity_type}: {command if command else line[:100]}"

            metadata = {
                'raw_line': line,
                'activity_type': activity_type,
                'command': command
            }

            return Event(
                timestamp=timestamp,
                source=EventSource.C2_LOG,
                event_type=EventType.C2_COMMAND,
                category=category,
                description=description,
                severity=self._determine_c2_severity(activity_type),
                metadata=metadata
            )

        except Exception as e:
            logger.debug(f"Failed to parse text line: {e}")
            return None

    def _extract_timestamp_from_dict(self, log_entry: Dict[str, Any]) -> Optional[datetime]:
        """Extract timestamp from a dictionary entry."""
        # Try common timestamp field names
        for time_field in ['timestamp', 'time', 'datetime', 'date', 'created_at']:
            if time_field in log_entry:
                return self._parse_timestamp_string(str(log_entry[time_field]))
        return None

    def _parse_timestamp_from_line(self, line: str) -> Optional[datetime]:
        """Extract timestamp from a text line."""
        for pattern, fmt in self.compiled_patterns:
            match = pattern.search(line)
            if match:
                timestamp_str = match.group(1)
                return self._parse_timestamp_string(timestamp_str, fmt)
        return None

    def _parse_timestamp_string(self, timestamp_str: str, fmt: str = None) -> Optional[datetime]:
        """Parse a timestamp string using various formats."""
        try:
            # If format is specified, use it
            if fmt:
                if fmt == 'unix':
                    # Unix timestamp
                    timestamp_float = float(timestamp_str)
                    return datetime.fromtimestamp(timestamp_float, tz=pytz.UTC)
                else:
                    # Standard format
                    if 'Z' in timestamp_str:
                        timestamp_str = timestamp_str.rstrip('Z')
                    return datetime.strptime(timestamp_str, fmt)

            # Try all patterns if no format specified
            for pattern, pattern_fmt in self.compiled_patterns:
                try:
                    if pattern_fmt == 'unix':
                        timestamp_float = float(timestamp_str)
                        return datetime.fromtimestamp(timestamp_float, tz=pytz.UTC)
                    else:
                        if 'Z' in timestamp_str:
                            timestamp_str = timestamp_str.rstrip('Z')
                        return datetime.strptime(timestamp_str, pattern_fmt)
                except (ValueError, TypeError):
                    continue

            return None

        except Exception as e:
            logger.debug(f"Failed to parse timestamp '{timestamp_str}': {e}")
            return None

    def _classify_c2_activity(self, command: str) -> str:
        """Classify C2 activity based on command content."""
        if not command:
            return 'unknown'

        command_lower = command.lower()

        # Check against known patterns
        for activity_type, pattern in self.c2_patterns.items():
            if pattern.search(command):
                return activity_type

        # Additional classification based on keywords
        if any(word in command_lower for word in ['powershell', 'cmd', 'bash', 'sh']):
            return 'command'
        elif any(word in command_lower for word in ['wget', 'curl', 'download']):
            return 'download'
        elif any(word in command_lower for word in ['nc', 'netcat', 'reverse', 'shell']):
            return 'command'
        elif any(word in command_lower for word in ['mimikatz', 'hashdump', 'password']):
            return 'privilege_escalation'
        else:
            return 'unknown'

    def _extract_command_from_line(self, line: str) -> Optional[str]:
        """Extract command from a text line."""
        # Look for common command patterns
        patterns = [
            r'(?:cmd|command|exec):\s*(.+)',
            r'(?:execute|run):\s*(.+)',
            r'>\s*(.+)',
            r'\$\s*(.+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                return match.group(1).strip()

        # If no specific pattern, return the line after timestamp
        for pattern, _ in self.compiled_patterns:
            match = pattern.search(line)
            if match:
                # Return everything after the timestamp
                remaining = line[match.end():].strip()
                if remaining:
                    return remaining

        return None

    def _determine_c2_severity(self, activity_type: str) -> str:
        """Determine severity based on C2 activity type."""
        high_severity = [
            'privilege_escalation', 'lateral_movement', 'exfiltration',
            'persistence', 'command'
        ]

        medium_severity = [
            'download', 'upload', 'reconnaissance'
        ]

        if activity_type in high_severity:
            return 'high'
        elif activity_type in medium_severity:
            return 'medium'
        else:
            return 'low'