"""
Command and Control (C2) log parser for forensic timeline analysis.
"""

import os
import re
import json
import csv
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
import pytz

from ..models.event import Event, EventType, EventSource, EventCategory

logger = logging.getLogger(__name__)

class C2LogParser:
    """
    Parser for Command and Control (C2) server logs.

    This parser handles various C2 log formats including CSV, JSON, and
    plain text logs with configurable timestamp patterns.
    """

    def __init__(self):
        # Common timestamp patterns for C2 logs
        self.timestamp_patterns = [
            # ISO format: "2023-01-15 14:30:25"
            (r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', '%Y-%m-%d %H:%M:%S'),

            # RFC3339: "2023-01-15T14:30:25Z"
            (r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z?)', '%Y-%m-%dT%H:%M:%S'),

            # Unix timestamp
            (r'(\d{10}(?:\.\d{3})?)', 'unix'),

            # US format: "01/15/2023 14:30:25"
            (r'(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2})', '%m/%d/%Y %H:%M:%S'),

            # European format: "15/01/2023 14:30:25"
            (r'(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2})', '%d/%m/%Y %H:%M:%S'),

            # Log format: "[2023-01-15 14:30:25]"
            (r'\[(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\]', '%Y-%m-%d %H:%M:%S'),
        ]

        # Compile regex patterns
        self.compiled_patterns = [(re.compile(pattern), fmt) for pattern, fmt in self.timestamp_patterns]

        # C2 command patterns
        self.c2_patterns = {
            'beacon': re.compile(r'(?:beacon|checkin|heartbeat)', re.IGNORECASE),
            'command': re.compile(r'(?:execute|exec|cmd|run|shell)', re.IGNORECASE),
            'download': re.compile(r'(?:download|get|fetch|retrieve)', re.IGNORECASE),
            'upload': re.compile(r'(?:upload|put|send|transfer)', re.IGNORECASE),
            'persistence': re.compile(r'(?:persist|install|startup|autorun)', re.IGNORECASE),
            'lateral_movement': re.compile(r'(?:lateral|pivot|jump|spread)', re.IGNORECASE),
            'exfiltration': re.compile(r'(?:exfil|steal|copy|backup)', re.IGNORECASE),
            'reconnaissance': re.compile(r'(?:recon|scan|enum|discover)', re.IGNORECASE),
            'privilege_escalation': re.compile(r'(?:elevate|escalate|admin|root)', re.IGNORECASE),
        }

    def parse_c2_log_file(self, log_path: str, category: EventCategory,
                         log_format: str = 'auto') -> List[Event]:
        """
        Parse a C2 log file and extract events.

        Args:
            log_path: Path to the C2 log file
            category: Event category (baseline/attack)
            log_format: Format hint ('auto', 'json', 'csv', 'text')

        Returns:
            List of Event objects
        """
        if not os.path.exists(log_path):
            raise FileNotFoundError(f"C2 log file not found: {log_path}")

        logger.info(f"Parsing C2 log file: {log_path}")

        # Auto-detect format if not specified
        if log_format == 'auto':
            log_format = self._detect_format(log_path)

        events = []

        try:
            if log_format == 'json':
                events = self._parse_json_log(log_path, category)
            elif log_format == 'csv':
                events = self._parse_csv_log(log_path, category)
            else:  # text format
                events = self._parse_text_log(log_path, category)

        except Exception as e:
            logger.error(f"Failed to parse C2 log file {log_path}: {e}")
            raise

        logger.info(f"Extracted {len(events)} events from {log_path}")
        return events

    def _detect_format(self, log_path: str) -> str:
        """Auto-detect the log file format."""
        try:
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                first_line = f.readline().strip()

                # Check for JSON
                if first_line.startswith('{') and first_line.endswith('}'):
                    try:
                        json.loads(first_line)
                        return 'json'
                    except json.JSONDecodeError:
                        pass

                # Check for CSV (look for commas and potential headers)
                if ',' in first_line and len(first_line.split(',')) > 2:
                    return 'csv'

                # Default to text
                return 'text'

        except Exception:
            return 'text'

    def _parse_json_log(self, log_path: str, category: EventCategory) -> List[Event]:
        """Parse JSON format C2 logs."""
        events = []

        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    log_entry = json.loads(line)
                    event = self._convert_json_entry(log_entry, category)
                    if event:
                        events.append(event)
                except json.JSONDecodeError as e:
                    logger.debug(f"Failed to parse JSON line {line_num}: {e}")
                    continue
                except Exception as e:
                    logger.debug(f"Failed to convert JSON entry on line {line_num}: {e}")
                    continue

        return events

    def _parse_csv_log(self, log_path: str, category: EventCategory) -> List[Event]:
        """Parse CSV format C2 logs."""
        events = []

        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            # Try to detect delimiter
            sample = f.read(1024)
            f.seek(0)

            delimiter = ','
            if ';' in sample and sample.count(';') > sample.count(','):
                delimiter = ';'
            elif '\t' in sample:
                delimiter = '\t'

            reader = csv.DictReader(f, delimiter=delimiter)

            for row_num, row in enumerate(reader, 1):
                try:
                    event = self._convert_csv_row(row, category)
                    if event:
                        events.append(event)
                except Exception as e:
                    logger.debug(f"Failed to convert CSV row {row_num}: {e}")
                    continue

        return events