"""
Linux syslog parser for forensic timeline analysis.
"""

import os
import re
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Pattern
import pytz

from ..models.event import Event, EventType, EventSource, EventCategory

logger = logging.getLogger(__name__)

class SyslogParser:
    """
    Parser for Linux syslog files.

    This parser handles various syslog formats and extracts security-relevant
    events for forensic timeline analysis.
    """

    def __init__(self):
        # Common syslog timestamp patterns
        self.timestamp_patterns = [
            # Standard syslog: "Jan 15 14:30:25"
            (r'^(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})', '%b %d %H:%M:%S'),

            # RFC3339: "2023-01-15T14:30:25.123Z"
            (r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3})?Z?)', '%Y-%m-%dT%H:%M:%S'),

            # ISO format: "2023-01-15 14:30:25"
            (r'^(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', '%Y-%m-%d %H:%M:%S'),

            # Rsyslog high precision: "2023-01-15T14:30:25.123456+00:00"
            (r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{6}[+-]\d{2}:\d{2})', '%Y-%m-%dT%H:%M:%S.%f%z'),
        ]

        # Compile regex patterns for performance
        self.compiled_patterns = [(re.compile(pattern), fmt) for pattern, fmt in self.timestamp_patterns]

        # Security-relevant log patterns
        self.security_patterns = {
            # SSH authentication
            'ssh_success': re.compile(r'sshd.*Accepted\s+(\w+)\s+for\s+(\w+)\s+from\s+([\d.]+)\s+port\s+(\d+)'),
            'ssh_failure': re.compile(r'sshd.*Failed\s+(\w+)\s+for\s+(?:invalid user\s+)?(\w+)\s+from\s+([\d.]+)\s+port\s+(\d+)'),
            'ssh_disconnect': re.compile(r'sshd.*Disconnected from\s+(?:user\s+)?(\w+)\s+([\d.]+)\s+port\s+(\d+)'),

            # Sudo usage
            'sudo_success': re.compile(r'sudo.*(\w+)\s*:\s*TTY=(\S+)\s*;\s*PWD=(\S+)\s*;\s*USER=(\w+)\s*;\s*COMMAND=(.+)'),
            'sudo_failure': re.compile(r'sudo.*(\w+)\s*:\s*(\d+)\s+incorrect password attempts'),

            # Authentication
            'auth_success': re.compile(r'(?:login|su).*session opened for user (\w+)'),
            'auth_failure': re.compile(r'(?:login|su).*authentication failure.*user=(\w+)'),

            # Process execution
            'process_start': re.compile(r'systemd.*Started (.+)'),
            'process_stop': re.compile(r'systemd.*Stopped (.+)'),

            # Kernel messages
            'kernel_oops': re.compile(r'kernel.*Oops:'),
            'kernel_panic': re.compile(r'kernel.*Kernel panic'),
            'segfault': re.compile(r'kernel.*segfault at'),

            # Network events
            'firewall_block': re.compile(r'(?:iptables|ufw).*BLOCK.*SRC=([\d.]+).*DST=([\d.]+).*PROTO=(\w+)'),
            'firewall_allow': re.compile(r'(?:iptables|ufw).*ALLOW.*SRC=([\d.]+).*DST=([\d.]+).*PROTO=(\w+)'),

            # File system events
            'mount': re.compile(r'kernel.*mounted filesystem.*on (.+)'),
            'umount': re.compile(r'kernel.*unmounted filesystem.*from (.+)'),

            # Service events
            'service_start': re.compile(r'systemd.*Starting (.+)'),
            'service_stop': re.compile(r'systemd.*Stopping (.+)'),
            'service_fail': re.compile(r'systemd.*Failed to start (.+)'),

            # Cron events
            'cron_start': re.compile(r'CRON.*\((\w+)\) CMD \((.+)\)'),

            # Package management
            'package_install': re.compile(r'(?:apt|yum|dnf).*install.*(\S+)'),
            'package_remove': re.compile(r'(?:apt|yum|dnf).*remove.*(\S+)'),
        }

    def parse_syslog_file(self, log_path: str, category: EventCategory) -> List[Event]:
        """
        Parse a syslog file and extract events.

        Args:
            log_path: Path to the syslog file
            category: Event category (baseline/attack)

        Returns:
            List of Event objects
        """
        if not os.path.exists(log_path):
            raise FileNotFoundError(f"Syslog file not found: {log_path}")

        logger.info(f"Parsing syslog file: {log_path}")
        events = []

        try:
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        event = self._parse_syslog_line(line, category)
                        if event:
                            events.append(event)
                    except Exception as e:
                        logger.debug(f"Failed to parse line {line_num}: {e}")
                        continue

        except Exception as e:
            logger.error(f"Failed to read syslog file {log_path}: {e}")
            raise

        logger.info(f"Extracted {len(events)} events from {log_path}")
        return events

    def _parse_syslog_line(self, line: str, category: EventCategory) -> Optional[Event]:
        """Parse a single syslog line into an Event object."""
        try:
            # Extract timestamp
            timestamp = self._extract_timestamp(line)
            if not timestamp:
                return None

            # Check for security-relevant patterns
            for pattern_name, pattern in self.security_patterns.items():
                match = pattern.search(line)
                if match:
                    return self._create_event_from_pattern(
                        timestamp, line, pattern_name, match, category
                    )

            # If no specific pattern matches, check for general suspicious indicators
            if self._is_suspicious_line(line):
                return self._create_generic_event(timestamp, line, category)

            return None

        except Exception as e:
            logger.debug(f"Failed to parse syslog line: {e}")
            return None

    def _extract_timestamp(self, line: str) -> Optional[datetime]:
        """Extract timestamp from syslog line."""
        for pattern, fmt in self.compiled_patterns:
            match = pattern.match(line)
            if match:
                timestamp_str = match.group(1)
                try:
                    # Handle different timestamp formats
                    if fmt == '%b %d %H:%M:%S':
                        # Standard syslog format needs year
                        current_year = datetime.now().year
                        timestamp_str = f"{current_year} {timestamp_str}"
                        timestamp = datetime.strptime(timestamp_str, f'%Y %b %d %H:%M:%S')
                    elif 'T' in fmt and 'Z' in timestamp_str:
                        # Handle UTC timestamps
                        timestamp_str = timestamp_str.rstrip('Z')
                        if '.' in timestamp_str:
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%dT%H:%M:%S.%f')
                        else:
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%dT%H:%M:%S')
                        timestamp = timestamp.replace(tzinfo=pytz.UTC)
                    else:
                        timestamp = datetime.strptime(timestamp_str, fmt)

                    return timestamp

                except ValueError as e:
                    logger.debug(f"Failed to parse timestamp '{timestamp_str}': {e}")
                    continue

        return None

    def _create_event_from_pattern(self, timestamp: datetime, line: str,
                                 pattern_name: str, match, category: EventCategory) -> Event:
        """Create an Event object from a matched pattern."""

        # Extract metadata based on pattern type
        metadata = {'raw_log': line, 'pattern_type': pattern_name}

        if pattern_name.startswith('ssh_'):
            if pattern_name == 'ssh_success':
                auth_method, username, source_ip, port = match.groups()
                metadata.update({
                    'auth_method': auth_method,
                    'username': username,
                    'source_ip': source_ip,
                    'port': port
                })
                description = f"SSH successful login: {username} from {source_ip}"
                event_type = EventType.AUTHENTICATION
                severity = "medium"

            elif pattern_name == 'ssh_failure':
                auth_method, username, source_ip, port = match.groups()
                metadata.update({
                    'auth_method': auth_method,
                    'username': username,
                    'source_ip': source_ip,
                    'port': port
                })
                description = f"SSH failed login: {username} from {source_ip}"
                event_type = EventType.AUTHENTICATION
                severity = "high"

            else:  # ssh_disconnect
                username, source_ip, port = match.groups()
                metadata.update({
                    'username': username,
                    'source_ip': source_ip,
                    'port': port
                })
                description = f"SSH disconnection: {username} from {source_ip}"
                event_type = EventType.AUTHENTICATION
                severity = "low"

        elif pattern_name.startswith('sudo_'):
            if pattern_name == 'sudo_success':
                username, tty, pwd, target_user, command = match.groups()
                metadata.update({
                    'username': username,
                    'tty': tty,
                    'working_directory': pwd,
                    'target_user': target_user,
                    'command': command
                })
                description = f"Sudo execution: {username} -> {target_user}: {command}"
                event_type = EventType.PROCESS_CREATION
                severity = "medium"

            else:  # sudo_failure
                username, attempts = match.groups()
                metadata.update({
                    'username': username,
                    'failed_attempts': attempts
                })
                description = f"Sudo authentication failure: {username} ({attempts} attempts)"
                event_type = EventType.AUTHENTICATION
                severity = "high"

        elif pattern_name.startswith('kernel_'):
            description = f"Kernel event: {pattern_name}"
            event_type = EventType.SYSTEM_LOG
            severity = "high" if pattern_name in ['kernel_panic', 'kernel_oops'] else "medium"

        elif pattern_name.startswith('firewall_'):
            src_ip, dst_ip, proto = match.groups()
            metadata.update({
                'source_ip': src_ip,
                'destination_ip': dst_ip,
                'protocol': proto
            })
            action = "blocked" if pattern_name == 'firewall_block' else "allowed"
            description = f"Firewall {action}: {src_ip} -> {dst_ip} ({proto})"
            event_type = EventType.NETWORK_CONNECTION
            severity = "medium" if pattern_name == 'firewall_block' else "low"

        else:
            # Generic handling for other patterns
            description = f"System event: {pattern_name}"
            event_type = EventType.SYSTEM_LOG
            severity = "low"

        return Event(
            timestamp=timestamp,
            source=EventSource.LINUX_SYSLOG,
            event_type=event_type,
            category=category,
            description=description,
            severity=severity,
            metadata=metadata
        )

    def _is_suspicious_line(self, line: str) -> bool:
        """Check if a log line contains suspicious indicators."""
        suspicious_keywords = [
            'error', 'fail', 'denied', 'refused', 'blocked', 'rejected',
            'unauthorized', 'forbidden', 'invalid', 'illegal', 'violation',
            'attack', 'intrusion', 'malware', 'virus', 'trojan', 'backdoor',
            'exploit', 'payload', 'shellcode', 'injection', 'overflow'
        ]

        line_lower = line.lower()
        return any(keyword in line_lower for keyword in suspicious_keywords)

    def _create_generic_event(self, timestamp: datetime, line: str,
                            category: EventCategory) -> Event:
        """Create a generic event for suspicious but unmatched log lines."""

        metadata = {'raw_log': line}

        # Try to extract basic information
        if 'error' in line.lower():
            description = "System error detected"
            severity = "medium"
        elif any(word in line.lower() for word in ['fail', 'denied', 'refused']):
            description = "System failure or access denied"
            severity = "medium"
        elif any(word in line.lower() for word in ['attack', 'intrusion', 'malware']):
            description = "Potential security incident"
            severity = "high"
        else:
            description = "Suspicious system activity"
            severity = "low"

        return Event(
            timestamp=timestamp,
            source=EventSource.LINUX_SYSLOG,
            event_type=EventType.SYSTEM_LOG,
            category=category,
            description=description,
            severity=severity,
            metadata=metadata
        )