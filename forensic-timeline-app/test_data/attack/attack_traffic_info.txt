
# Attack Network Traffic Information
# This file describes what would be in attack.pcap

Traffic Summary:
- Duration: 2 hours (2023-01-15 14:00:00 - 2023-01-15 16:00:00)
- Total packets: ~15,000
- Protocols: HTTP, HTTPS, SSH, DNS, TCP

Attack Timeline:
14:00-14:05: Normal baseline activity
14:05-14:15: SSH brute force attack from ********
14:15-14:20: Successful SSH compromise
14:20-14:30: Reconnaissance and enumeration
14:30-14:35: Persistence establishment
14:35-14:40: Lateral movement attempts
14:40-14:45: Data exfiltration
14:45-14:50: Anti-forensics and cleanup

Suspicious Network Flows:
- ******** -> *************:22 (SSH brute force - 200+ failed attempts)
- ************* -> ********:4444 (Reverse shell connection)
- ************* -> malicious.com:80 (Malware download)
- ************* -> ********:443 (C2 communication)
- ************* -> ********:22 (Data exfiltration via SCP)

IOCs (Indicators of Compromise):
- Source IP: ******** (attacker)
- Malicious domain: malicious.com
- C2 server: ********:443
- Backdoor port: 4444
