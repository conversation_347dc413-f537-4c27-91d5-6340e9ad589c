Jan 15 00:00:15 server1 systemd[1]: Started System Logging Service
Jan 15 00:00:20 server1 systemd[1]: Started OpenSSH server daemon
Jan 15 00:00:25 server1 systemd[1]: Started Apache HTTP Server
Jan 15 08:15:00 server1 sshd[7948]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 08:15:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /var/log/messages
Jan 15 08:15:45 server1 sshd[5231]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 08:30:00 server1 sshd[2079]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 08:30:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /var/log/messages
Jan 15 08:30:45 server1 sshd[2279]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 08:45:00 server1 sshd[1155]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 08:45:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 08:45:45 server1 sshd[6360]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 09:15:00 server1 sshd[2155]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 09:15:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 09:15:45 server1 sshd[1674]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 09:30:00 server1 sshd[8004]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 09:30:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 09:30:45 server1 sshd[4386]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 09:45:00 server1 sshd[9605]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 09:45:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 09:45:45 server1 sshd[2259]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 10:15:00 server1 sshd[7215]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 10:15:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 10:15:45 server1 sshd[3364]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 10:30:00 server1 sshd[5840]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 10:30:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /var/log/messages
Jan 15 10:30:45 server1 sshd[4362]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 10:45:00 server1 sshd[4913]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 10:45:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /var/log/messages
Jan 15 10:45:45 server1 sshd[9564]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 11:15:00 server1 sshd[9595]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 11:15:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/top
Jan 15 11:15:45 server1 sshd[7180]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 11:30:00 server1 sshd[5165]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 11:30:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/top
Jan 15 11:30:45 server1 sshd[2856]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 11:45:00 server1 sshd[2890]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 11:45:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /var/log/messages
Jan 15 11:45:45 server1 sshd[7807]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 12:15:00 server1 sshd[2699]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 12:15:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/top
Jan 15 12:15:45 server1 sshd[1909]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 12:30:00 server1 sshd[6341]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 12:30:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /var/log/messages
Jan 15 12:30:45 server1 sshd[3975]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 12:45:00 server1 sshd[9072]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 12:45:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /var/log/messages
Jan 15 12:45:45 server1 sshd[5824]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 13:15:00 server1 sshd[8906]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 13:15:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 13:15:45 server1 sshd[5410]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 13:30:00 server1 sshd[3015]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 13:30:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/systemctl status apache2
Jan 15 13:30:45 server1 sshd[5531]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 13:45:00 server1 sshd[2870]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 13:45:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 13:45:45 server1 sshd[9564]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 14:15:00 server1 sshd[6249]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 14:15:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/systemctl status apache2
Jan 15 14:15:45 server1 sshd[9187]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 14:30:00 server1 sshd[4365]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 14:30:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 14:30:45 server1 sshd[2454]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 14:45:00 server1 sshd[4843]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 14:45:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 14:45:45 server1 sshd[4968]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 15:15:00 server1 sshd[4777]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 15:15:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /var/log/messages
Jan 15 15:15:45 server1 sshd[9981]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 15:30:00 server1 sshd[4925]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 15:30:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /var/log/messages
Jan 15 15:30:45 server1 sshd[9411]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 15:45:00 server1 sshd[9755]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 15:45:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/top
Jan 15 15:45:45 server1 sshd[5019]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 16:15:00 server1 sshd[4678]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 16:15:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/systemctl status apache2
Jan 15 16:15:45 server1 sshd[8980]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 16:30:00 server1 sshd[6887]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 16:30:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/systemctl status apache2
Jan 15 16:30:45 server1 sshd[5327]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 16:45:00 server1 sshd[2610]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 16:45:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/systemctl status apache2
Jan 15 16:45:45 server1 sshd[3248]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 17:15:00 server1 sshd[2666]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 17:15:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /var/log/messages
Jan 15 17:15:45 server1 sshd[3185]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 17:30:00 server1 sshd[9982]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 17:30:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 17:30:45 server1 sshd[3354]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 17:45:00 server1 sshd[1572]: Accepted password for admin from 192.168.1.10 port 22 ssh2
Jan 15 17:45:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /var/log/messages
Jan 15 17:45:45 server1 sshd[7690]: Disconnected from user admin 192.168.1.10 port 22
Jan 15 00:00:00 server1 CRON[9443]: (root) CMD (/usr/bin/updatedb)
Jan 15 06:00:00 server1 CRON[3199]: (root) CMD (/usr/bin/updatedb)
Jan 15 12:00:00 server1 CRON[5115]: (root) CMD (/usr/bin/updatedb)
Jan 15 18:00:00 server1 CRON[4244]: (root) CMD (/usr/bin/updatedb)
