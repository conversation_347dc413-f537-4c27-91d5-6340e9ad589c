#!/usr/bin/env python3
"""
Integration test script for the Forensic Timeline Analysis application.
"""

import sys
import os

# Add the parent directory to Python path so we can import app
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_complete_analysis():
    """Test the complete analysis pipeline with sample data."""
    print("🚀 Starting Integration Test")
    print("=" * 50)

    try:
        from app.parsers.log_normalizer import LogNormalizer
        from app.models.event import EventCategory

        # Test data files
        test_files = {
            'baseline_logs': [
                {'filepath': 'baseline/baseline_system.log', 'file_type': 'syslog'}
            ],
            'attack_logs': [
                {'filepath': 'attack/attack_system.log', 'file_type': 'syslog'},
                {'filepath': 'attack/c2_server.json', 'file_type': 'c2'},
                {'filepath': 'attack/c2_commands.csv', 'file_type': 'c2'}
            ]
        }

        normalizer = LogNormalizer()

        # Parse baseline events
        print("\n📊 Parsing baseline events...")
        baseline_events = normalizer.parse_log_files(test_files['baseline_logs'], EventCategory.BASELINE)
        print(f"✅ Baseline events: {len(baseline_events)}")

        # Parse attack events
        print("\n🚨 Parsing attack events...")
        attack_events = normalizer.parse_log_files(test_files['attack_logs'], EventCategory.ATTACK)
        print(f"✅ Attack events: {len(attack_events)}")

        # Combine and analyze
        all_events = baseline_events + attack_events
        all_events.sort(key=lambda e: e.timestamp)

        print(f"\n📈 Total events in timeline: {len(all_events)}")

        # Show event summary
        from collections import Counter

        source_counts = Counter(e.source.value for e in all_events)
        category_counts = Counter(e.category.value for e in all_events)
        severity_counts = Counter(e.severity for e in all_events if e.severity)

        print("\n📊 Event Summary:")
        print(f"  By Source: {dict(source_counts)}")
        print(f"  By Category: {dict(category_counts)}")
        print(f"  By Severity: {dict(severity_counts)}")

        # Show timeline sample
        print("\n⏰ Timeline Sample (first 10 events):")
        for i, event in enumerate(all_events[:10]):
            print(f"  {i+1:2d}. {event.timestamp} | {event.source.value:12s} | {event.description[:60]}")

        print("\n🎉 Integration test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_complete_analysis()
    sys.exit(0 if success else 1)
