%YAML 1.1
---
# Suricata configuration for offline PCAP analysis
# Optimized for forensic timeline analysis

# Global settings
vars:
  address-groups:
    HOME_NET: "[***********/16,10.0.0.0/8,**********/12]"
    EXTERNAL_NET: "!$HOME_NET"
    HTTP_SERVERS: "$HOME_NET"
    SMTP_SERVERS: "$HOME_NET"
    SQL_SERVERS: "$HOME_NET"
    DNS_SERVERS: "$HOME_NET"
    TELNET_SERVERS: "$HOME_NET"
    AIM_SERVERS: "$EXTERNAL_NET"
    DC_SERVERS: "$HOME_NET"
    DNP3_SERVER: "$HOME_NET"
    DNP3_CLIENT: "$HOME_NET"
    MODBUS_CLIENT: "$HOME_NET"
    MODBUS_SERVER: "$HOME_NET"
    ENIP_CLIENT: "$HOME_NET"
    ENIP_SERVER: "$HOME_NET"

  port-groups:
    HTTP_PORTS: "80"
    SHELLCODE_PORTS: "!80"
    ORACLE_PORTS: 1521
    SSH_PORTS: 22
    DNP3_PORTS: 20000
    MODBUS_PORTS: 502
    FILE_DATA_PORTS: "[$HTTP_PORTS,110,143]"
    FTP_PORTS: 21
    GENEVE_PORTS: 6081
    VXLAN_PORTS: 4789
    TEREDO_PORTS: 3544

# Default rule path
default-rule-path: /var/lib/suricata/rules

# Rule files for forensic analysis
rule-files:
  - emerging-all.rules
  - emerging-malware.rules
  - emerging-exploit.rules
  - emerging-trojan.rules
  - emerging-botcc.rules
  - emerging-compromised.rules
  - emerging-drop.rules
  - emerging-dshield.rules
  - emerging-tor.rules
  - emerging-dns.rules
  - emerging-web_client.rules
  - emerging-web_server.rules
  - emerging-policy.rules

# Classification config
classification-file: /etc/suricata/classification.config
reference-config-file: /etc/suricata/reference.config

# Logging configuration for forensic analysis
outputs:
  # EVE JSON log - primary output for timeline analysis
  - eve-log:
      enabled: yes
      filetype: regular
      filename: eve.json
      types:
        - alert:
            payload: yes
            payload-buffer-size: 4kb
            payload-printable: yes
            packet: yes
            metadata: yes
            http-body: yes
            http-body-printable: yes
            tagged-packets: yes
        - http:
            extended: yes
        - dns:
            query: yes
            answer: yes
        - tls:
            extended: yes
        - files:
            force-magic: no
        - smtp:
        - ssh
        - stats:
            totals: yes
            threads: no
            deltas: no
        - flow
        - netflow

  # Fast log for quick alert overview
  - fast:
      enabled: yes
      filename: fast.log
      append: yes

  # Stats log
  - stats:
      enabled: yes
      filename: stats.log
      append: yes
      totals: yes
      threads: no

# Logging level
logging:
  default-log-level: notice
  default-output-filter:
  outputs:
  - console:
      enabled: yes
  - file:
      enabled: yes
      level: info
      filename: /var/log/suricata/suricata.log
  - syslog:
      enabled: no

# Application layer parsers
app-layer:
  protocols:
    http:
      enabled: yes
      memcap: 64mb
    ftp:
      enabled: yes
      memcap: 64mb
    tls:
      enabled: yes
      detection-ports:
        dp: 443
    ssh:
      enabled: yes
    smtp:
      enabled: yes
      mime:
        decode-mime: yes
        decode-base64: yes
        decode-quoted-printable: yes
        header-value-depth: 2000
        extract-urls: yes
        body-md5: yes
    dns:
      tcp:
        enabled: yes
        detection-ports:
          dp: 53
      udp:
        enabled: yes
        detection-ports:
          dp: 53

# Performance tuning for offline analysis
threading:
  set-cpu-affinity: no
  cpu-affinity:
    - management-cpu-set:
        cpu: [ 0 ]
    - receive-cpu-set:
        cpu: [ 0 ]
    - worker-cpu-set:
        cpu: [ "all" ]
  detect-thread-ratio: 1.0