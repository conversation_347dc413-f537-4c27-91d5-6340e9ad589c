# Forensic Timeline Analysis Application
# Multi-stage Docker build for production-ready container

FROM ubuntu:20.04 AS base

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    curl \
    wget \
    git \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# Create application user
RUN useradd -m -u 1000 forensic && \
    mkdir -p /app /data /logs && \
    chown -R forensic:forensic /app /data /logs

# Install Python dependencies
COPY requirements.txt /tmp/
RUN pip3 install --no-cache-dir -r /tmp/requirements.txt

# Stage 2: Add Suricata
FROM base AS suricata-stage

# Add Suricata repository and install
RUN add-apt-repository ppa:oisf/suricata-stable && \
    apt-get update && \
    apt-get install -y suricata && \
    rm -rf /var/lib/apt/lists/*

# Download and install Emerging Threats rules
RUN mkdir -p /var/lib/suricata/rules && \
    wget -O /tmp/emerging.rules.tar.gz \
    "https://rules.emergingthreats.net/open/suricata/emerging.rules.tar.gz" && \
    tar -xzf /tmp/emerging.rules.tar.gz -C /var/lib/suricata/rules --strip-components=1 && \
    rm /tmp/emerging.rules.tar.gz

# Configure Suricata for offline analysis
COPY suricata.yaml /etc/suricata/suricata.yaml

# Stage 3: Add Zeek
FROM suricata-stage AS zeek-stage

# Install Zeek dependencies
RUN apt-get update && apt-get install -y \
    cmake \
    make \
    gcc \
    g++ \
    flex \
    bison \
    libpcap-dev \
    libssl-dev \
    python3-dev \
    swig \
    zlib1g-dev \
    && rm -rf /var/lib/apt/lists/*

# Download and compile Zeek
WORKDIR /tmp
RUN wget https://download.zeek.org/zeek-4.2.1.tar.gz && \
    tar -xzf zeek-4.2.1.tar.gz && \
    cd zeek-4.2.1 && \
    ./configure --prefix=/usr/local/zeek && \
    make && \
    make install && \
    cd / && \
    rm -rf /tmp/zeek-4.2.1*

# Add Zeek to PATH
ENV PATH="/usr/local/zeek/bin:${PATH}"

# Stage 4: Final application stage
FROM zeek-stage AS final

# Switch to application user
USER forensic
WORKDIR /app

# Copy application code
COPY --chown=forensic:forensic . /app/

# Create necessary directories
RUN mkdir -p /app/uploads /app/temp /app/results /app/logs

# Expose port for web interface
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1

# Default command
CMD ["python3", "run.py"]