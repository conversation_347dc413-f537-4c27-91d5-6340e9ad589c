# Core Flask dependencies
Flask==2.3.3
Werkzeug==2.3.7
Jinja2==3.1.2

# Database
SQLAlchemy==2.0.21
Flask-SQLAlchemy==3.0.5

# File handling and validation
python-magic==0.4.27
Pillow==10.0.1

# Network analysis and parsing
scapy==2.5.0
python-evtx==0.7.4
pytz==2023.3

# Data processing (compatible with Python 3.8)
pandas==2.0.3
numpy==1.24.4

# JSON and data serialization
jsonschema==4.17.3

# HTTP client for health checks
requests==2.28.2

# CLI argument parsing
click==8.1.3

# Logging and monitoring
structlog==22.3.0

# Testing (for development)
pytest==7.4.2
pytest-flask==1.2.0
pytest-cov==4.1.0

# Development tools
black==23.9.1
flake8==6.1.0
mypy==1.5.1

# Security
cryptography==41.0.4

# Date/time handling
python-dateutil==2.8.2

# Configuration management
python-dotenv==1.0.0

# Progress bars for CLI
tqdm==4.66.1

# Memory profiling (optional)
memory-profiler==0.61.0

# Async support (for future WebSocket implementation)
Flask-SocketIO==5.3.6
eventlet==0.33.3