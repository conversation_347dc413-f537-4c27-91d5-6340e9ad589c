#!/usr/bin/env python3
"""
Comprehensive test suite for the Forensic Timeline Analysis application.
Tests all major functionality according to specifications.
"""

import os
import sys
import json
import time
import requests
import subprocess
from pathlib import Path
import tempfile
import shutil

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

class ForensicAnalysisTestSuite:
    """Comprehensive test suite for forensic analysis application."""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.test_results_dir = self.base_dir / "test_results_validation"
        self.test_results_dir.mkdir(exist_ok=True)
        
        self.web_url = "http://localhost:5000"
        self.test_passed = 0
        self.test_failed = 0
        self.test_results = []
    
    def log_test(self, test_name, passed, message=""):
        """Log test result."""
        status = "✅ PASS" if passed else "❌ FAIL"
        result = f"{status}: {test_name}"
        if message:
            result += f" - {message}"
        
        print(result)
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'message': message
        })
        
        if passed:
            self.test_passed += 1
        else:
            self.test_failed += 1
    
    def test_web_server_health(self):
        """Test web server health and API endpoints."""
        print("\n🌐 Testing Web Server Health...")
        
        try:
            # Test health endpoint
            response = requests.get(f"{self.web_url}/api/health", timeout=5)
            health_ok = response.status_code == 200 and "healthy" in response.json().get("status", "")
            self.log_test("Web Server Health Check", health_ok, f"Status: {response.status_code}")
            
            # Test main page
            response = requests.get(f"{self.web_url}/", timeout=5)
            main_page_ok = response.status_code == 200 and "Forensic Timeline Analysis" in response.text
            self.log_test("Main Page Load", main_page_ok, f"Status: {response.status_code}")
            
            return health_ok and main_page_ok
            
        except Exception as e:
            self.log_test("Web Server Health Check", False, f"Error: {e}")
            return False
    
    def test_cli_commands(self):
        """Test CLI command functionality."""
        print("\n💻 Testing CLI Commands...")
        
        try:
            # Test version command
            result = subprocess.run([
                "python", "cli.py", "version"
            ], capture_output=True, text=True, cwd=self.base_dir)
            
            version_ok = result.returncode == 0 and "Forensic Timeline Analysis" in result.stdout
            self.log_test("CLI Version Command", version_ok, f"Return code: {result.returncode}")
            
            # Test help command
            result = subprocess.run([
                "python", "cli.py", "--help"
            ], capture_output=True, text=True, cwd=self.base_dir)
            
            help_ok = result.returncode == 0 and "analyze" in result.stdout
            self.log_test("CLI Help Command", help_ok, f"Return code: {result.returncode}")
            
            return version_ok and help_ok
            
        except Exception as e:
            self.log_test("CLI Commands", False, f"Error: {e}")
            return False
    
    def test_log_parsing(self):
        """Test log parsing functionality."""
        print("\n📝 Testing Log Parsing...")
        
        try:
            # Test C2 log parsing
            c2_log_file = self.base_dir / "test_data/attack/c2_server.json"
            output_file = self.test_results_dir / "test_c2_parsing.json"
            
            result = subprocess.run([
                "python", "cli.py", "parse-logs",
                "--input", str(c2_log_file),
                "--category", "attack",
                "--output", str(output_file)
            ], capture_output=True, text=True, cwd=self.base_dir)
            
            parsing_ok = result.returncode == 0 and output_file.exists()
            
            if parsing_ok:
                with open(output_file) as f:
                    data = json.load(f)
                    events_count = data.get('total_events', 0)
                    parsing_ok = events_count > 0
                    self.log_test("C2 Log Parsing", parsing_ok, f"Parsed {events_count} events")
            else:
                self.log_test("C2 Log Parsing", False, f"Return code: {result.returncode}")
            
            return parsing_ok
            
        except Exception as e:
            self.log_test("Log Parsing", False, f"Error: {e}")
            return False
    
    def test_event_correlation(self):
        """Test event correlation functionality."""
        print("\n🔗 Testing Event Correlation...")
        
        try:
            # Use existing parsed events
            events_file = self.test_results_dir / "test_c2_parsing.json"
            correlations_file = self.test_results_dir / "test_correlations.json"
            
            if not events_file.exists():
                self.log_test("Event Correlation", False, "No events file for correlation test")
                return False
            
            result = subprocess.run([
                "python", "cli.py", "correlate",
                "--events", str(events_file),
                "--output", str(correlations_file),
                "--threshold", "0.2"
            ], capture_output=True, text=True, cwd=self.base_dir)
            
            correlation_ok = result.returncode == 0 and correlations_file.exists()
            
            if correlation_ok:
                with open(correlations_file) as f:
                    data = json.load(f)
                    correlations_count = data.get('total_correlations', 0)
                    correlation_ok = correlations_count > 0
                    self.log_test("Event Correlation", correlation_ok, f"Found {correlations_count} correlations")
            else:
                self.log_test("Event Correlation", False, f"Return code: {result.returncode}")
            
            return correlation_ok
            
        except Exception as e:
            self.log_test("Event Correlation", False, f"Error: {e}")
            return False
    
    def test_baseline_comparison(self):
        """Test baseline vs attack comparison."""
        print("\n🔍 Testing Baseline vs Attack Comparison...")
        
        try:
            # Create dummy baseline events for comparison
            baseline_file = self.test_results_dir / "dummy_baseline.json"
            attack_file = self.test_results_dir / "test_c2_parsing.json"
            comparison_file = self.test_results_dir / "test_comparison.json"
            
            # Create minimal baseline data
            baseline_data = {
                "total_events": 0,
                "events": []
            }
            
            with open(baseline_file, 'w') as f:
                json.dump(baseline_data, f)
            
            if not attack_file.exists():
                self.log_test("Baseline Comparison", False, "No attack events for comparison")
                return False
            
            result = subprocess.run([
                "python", "cli.py", "compare",
                "--baseline-events", str(baseline_file),
                "--attack-events", str(attack_file),
                "--output", str(comparison_file)
            ], capture_output=True, text=True, cwd=self.base_dir)
            
            comparison_ok = result.returncode == 0 and comparison_file.exists()
            
            if comparison_ok:
                with open(comparison_file) as f:
                    data = json.load(f)
                    risk_score = data.get('comparison_results', {}).get('comparison_summary', {}).get('risk_score', 0)
                    comparison_ok = risk_score >= 0
                    self.log_test("Baseline Comparison", comparison_ok, f"Risk score: {risk_score}")
            else:
                self.log_test("Baseline Comparison", False, f"Return code: {result.returncode}")
            
            return comparison_ok
            
        except Exception as e:
            self.log_test("Baseline Comparison", False, f"Error: {e}")
            return False
    
    def test_report_generation(self):
        """Test report generation functionality."""
        print("\n📊 Testing Report Generation...")
        
        try:
            # Test HTML report generation
            timeline_file = self.base_dir / "test_results/complete_analysis/timeline.json"
            correlations_file = self.test_results_dir / "test_correlations.json"
            comparison_file = self.test_results_dir / "test_comparison.json"
            report_file = self.test_results_dir / "test_report.html"
            
            if not timeline_file.exists():
                self.log_test("Report Generation", False, "No timeline file for report generation")
                return False
            
            result = subprocess.run([
                "python", "cli.py", "report",
                "--timeline", str(timeline_file),
                "--correlations", str(correlations_file) if correlations_file.exists() else "",
                "--comparison", str(comparison_file) if comparison_file.exists() else "",
                "--output", str(report_file),
                "--format", "html"
            ], capture_output=True, text=True, cwd=self.base_dir)
            
            report_ok = result.returncode == 0 and report_file.exists()
            
            if report_ok:
                with open(report_file) as f:
                    content = f.read()
                    report_ok = "Forensic Timeline Analysis Report" in content and len(content) > 1000
                    self.log_test("HTML Report Generation", report_ok, f"Report size: {len(content)} chars")
            else:
                self.log_test("HTML Report Generation", False, f"Return code: {result.returncode}")
            
            return report_ok
            
        except Exception as e:
            self.log_test("Report Generation", False, f"Error: {e}")
            return False
    
    def test_complete_analysis_pipeline(self):
        """Test the complete analysis pipeline."""
        print("\n🚀 Testing Complete Analysis Pipeline...")
        
        try:
            output_dir = self.test_results_dir / "pipeline_test"
            
            result = subprocess.run([
                "python", "cli.py", "analyze",
                "--attack-logs", "test_data/attack/c2_server.json",
                "--output", str(output_dir),
                "--correlate"
            ], capture_output=True, text=True, cwd=self.base_dir)
            
            pipeline_ok = result.returncode == 0
            
            if pipeline_ok:
                # Check if expected files were created
                expected_files = [
                    "timeline.json",
                    "correlations.json", 
                    "analysis_summary.json"
                ]
                
                files_exist = all((output_dir / f).exists() for f in expected_files)
                pipeline_ok = files_exist
                
                if files_exist:
                    # Check timeline content
                    with open(output_dir / "timeline.json") as f:
                        timeline_data = json.load(f)
                        events_count = timeline_data.get('total_events', 0)
                        pipeline_ok = events_count > 0
                        self.log_test("Complete Analysis Pipeline", pipeline_ok, f"Generated {events_count} events")
                else:
                    self.log_test("Complete Analysis Pipeline", False, "Missing expected output files")
            else:
                self.log_test("Complete Analysis Pipeline", False, f"Return code: {result.returncode}")
            
            return pipeline_ok
            
        except Exception as e:
            self.log_test("Complete Analysis Pipeline", False, f"Error: {e}")
            return False
    
    def test_web_api_endpoints(self):
        """Test web API endpoints."""
        print("\n🌐 Testing Web API Endpoints...")
        
        try:
            # Test sessions endpoint
            response = requests.get(f"{self.web_url}/api/sessions", timeout=5)
            sessions_ok = response.status_code == 200
            self.log_test("Sessions API Endpoint", sessions_ok, f"Status: {response.status_code}")
            
            # Test upload endpoint (GET for form)
            response = requests.get(f"{self.web_url}/upload", timeout=5)
            upload_ok = response.status_code == 200 and "upload" in response.text.lower()
            self.log_test("Upload Page Endpoint", upload_ok, f"Status: {response.status_code}")
            
            return sessions_ok and upload_ok
            
        except Exception as e:
            self.log_test("Web API Endpoints", False, f"Error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests and generate summary."""
        print("🧪 Starting Comprehensive Test Suite for Forensic Timeline Analysis")
        print("=" * 80)
        
        # Run all test categories
        tests = [
            self.test_web_server_health,
            self.test_cli_commands,
            self.test_log_parsing,
            self.test_event_correlation,
            self.test_baseline_comparison,
            self.test_report_generation,
            self.test_complete_analysis_pipeline,
            self.test_web_api_endpoints
        ]
        
        for test in tests:
            try:
                test()
            except Exception as e:
                print(f"❌ Test {test.__name__} failed with exception: {e}")
                self.test_failed += 1
        
        # Generate summary
        print("\n" + "=" * 80)
        print("🏁 TEST SUMMARY")
        print("=" * 80)
        print(f"✅ Tests Passed: {self.test_passed}")
        print(f"❌ Tests Failed: {self.test_failed}")
        print(f"📊 Success Rate: {(self.test_passed / (self.test_passed + self.test_failed) * 100):.1f}%")
        
        # Save detailed results
        results_file = self.test_results_dir / "test_summary.json"
        with open(results_file, 'w') as f:
            json.dump({
                'summary': {
                    'passed': self.test_passed,
                    'failed': self.test_failed,
                    'success_rate': self.test_passed / (self.test_passed + self.test_failed) * 100
                },
                'detailed_results': self.test_results
            }, f, indent=2)
        
        print(f"\n📁 Detailed results saved to: {results_file}")
        
        return self.test_failed == 0

def main():
    """Main test execution."""
    test_suite = ForensicAnalysisTestSuite()
    
    # Check if web server is running
    try:
        requests.get("http://localhost:5000/api/health", timeout=2)
        print("✅ Web server detected running on localhost:5000")
    except:
        print("⚠️  Web server not detected. Some tests may fail.")
        print("   Start the server with: python -m flask run")
    
    success = test_suite.run_all_tests()
    
    if success:
        print("\n🎉 ALL TESTS PASSED! Application is ready for deployment.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please review the results above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
