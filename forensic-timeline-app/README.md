# Forensic Timeline Analysis Application

A containerized forensic analysis tool that correlates network traffic captures (PCAPs) and system logs across baseline and attack scenarios to produce a combined timeline of events with interactive visualization.

## 🚀 Features

- **Multi-Interface Support**: Web UI, CLI, and REST API
- **Offline Analysis**: Complete analysis without internet connectivity
- **Network Analysis**: Integrated Suricata IDS and Zeek for comprehensive PCAP processing
- **Log Parsing**: Support for Windows EVTX, Linux syslogs, and custom C2 logs
- **Timeline Correlation**: Multi-source event correlation with interactive visualization
- **Baseline Comparison**: Highlight differences between normal and attack scenarios
- **Comprehensive Reporting**: HTML/PDF reports with findings and recommendations

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Interface │    │   CLI Interface  │    │   REST API      │
└─────────┬───────┘    └────────┬─────────┘    └─────────┬───────┘
          │                     │                        │
          └─────────────────────┼────────────────────────┘
                                │
                    ┌───────────▼───────────┐
                    │   Flask Application   │
                    └───────────┬───────────┘
                                │
                    ┌───────────▼───────────┐
                    │  Analysis Orchestrator │
                    └───────────┬───────────┘
                                │
        ┌───────────────────────┼───────────────────────┐
        │                       │                       │
┌───────▼────────┐    ┌─────────▼─────────┐    ┌───────▼────────┐
│ Network        │    │ Log Parsing       │    │ Event          │
│ Analysis       │    │ Engine            │    │ Correlation    │
│ (Suricata+Zeek)│    │ (EVTX/Syslog/C2)  │    │ Engine         │
└────────────────┘    └───────────────────┘    └────────────────┘
        │                       │                       │
        └───────────────────────┼───────────────────────┘
                                │
                    ┌───────────▼───────────┐
                    │   Timeline Builder    │
                    └───────────┬───────────┘
                                │
                    ┌───────────▼───────────┐
                    │ Visualization &       │
                    │ Report Generation     │
                    └───────────────────────┘
```

## 🛠️ Technology Stack

- **Backend**: Python 3 with Flask web framework
- **Containerization**: Docker with Ubuntu base
- **Network Analysis**: Suricata IDS + Zeek (Bro)
- **Frontend**: HTML5, JavaScript with vis.js timeline library
- **Log Parsing**: python-evtx, custom regex parsers
- **Data Storage**: SQLite for session data, in-memory for processing
- **API Documentation**: OpenAPI/Swagger

## 📦 Installation

### Prerequisites

- Docker and Docker Compose
- 4GB+ RAM (8GB recommended)
- 10GB+ storage space

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd forensic-timeline-app
   ```

2. **Build the Docker container**
   ```bash
   docker build -f docker/Dockerfile -t forensic-timeline-app .
   ```

3. **Run the application**
   ```bash
   # Web interface (recommended)
   docker run -p 5000:5000 -v $(pwd)/data:/data forensic-timeline-app

   # CLI interface
   docker run -it -v $(pwd)/data:/data forensic-timeline-app python cli.py --help
   ```

4. **Access the web interface**
   Open your browser to `http://localhost:5000`

## 🖥️ Usage

### Web Interface

1. Navigate to `http://localhost:5000`
2. Upload your baseline and attack PCAP files
3. Upload corresponding log files (optional)
4. Start the analysis
5. View the interactive timeline
6. Generate and download reports

### Command Line Interface

```bash
# Test tool installation
python cli.py test-tools

# Analyze a single PCAP
python cli.py analyze --pcap attack.pcap --category attack --output results/

# Compare baseline vs attack
python cli.py analyze --baseline-pcap baseline.pcap --attack-pcap attack.pcap --output results/

# Show version
python cli.py version
```

### REST API

```bash
# Health check
curl http://localhost:5000/api/health

# Upload files (multipart form data)
curl -X POST -F "attack_pcap=@attack.pcap" http://localhost:5000/api/upload

# Start analysis
curl -X POST -H "Content-Type: application/json" \
     -d '{"session_id": "your-session-id"}' \
     http://localhost:5000/api/analyze

# Get results
curl http://localhost:5000/api/results/your-session-id
```

## 📁 File Support

### Network Captures
- **PCAP Files**: `.pcap`, `.pcapng`

### Log Files
- **Windows Logs**: `.evtx` files
- **Linux Logs**: syslog, auth.log, custom text logs
- **C2 Logs**: CSV, JSON, plain text with timestamps

## 🔧 Configuration

The application can be configured through environment variables:

```bash
# Flask settings
FLASK_ENV=production
SECRET_KEY=your-secret-key

# Analysis settings
ANALYSIS_TIMEOUT=3600
MAX_CONTENT_LENGTH=**********  # 2GB

# Tool paths (auto-detected in container)
SURICATA_PATH=/usr/bin/suricata
ZEEK_PATH=/usr/local/zeek/bin/zeek
```

## 🧪 Development Status

### ✅ Completed (Phase 1-2)
- [x] Project structure and Docker environment
- [x] Flask application framework with routing
- [x] Core data models (Event, Timeline, Analysis)
- [x] Suricata IDS integration with offline analysis
- [x] Zeek network analyzer integration
- [x] PCAP analysis wrapper and event parsing
- [x] Basic CLI interface
- [x] REST API endpoints structure

### 🚧 In Progress (Phase 3-9)
- [ ] Log parsing engines (EVTX, syslog, C2 logs)
- [ ] Event correlation and timeline merger
- [ ] Interactive web interface with file upload
- [ ] Timeline visualization with vis.js
- [ ] Baseline vs attack comparison
- [ ] Report generation (HTML/PDF)
- [ ] Comprehensive testing and optimization

## 🔒 Security Features

- **Container Isolation**: All analysis runs in isolated Docker container
- **Local Processing**: No external data transmission required
- **Offline Operation**: Complete functionality without internet access
- **Input Validation**: Secure file handling and sanitization
- **Access Control**: Configurable authentication (if needed)

## 📊 Performance Requirements

- **Memory**: 4GB minimum, 8GB recommended
- **Storage**: 10GB for container + analysis workspace
- **CPU**: Multi-core recommended for large PCAP files
- **Network**: Offline operation (no internet required)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section in the documentation
2. Review existing GitHub issues
3. Create a new issue with detailed information

## 🔮 Roadmap

- **Phase 3**: Complete log parsing implementation
- **Phase 4**: Event correlation and timeline building
- **Phase 5**: Interactive web interface
- **Phase 6**: Advanced CLI and API features
- **Phase 7**: Baseline comparison algorithms
- **Phase 8**: Report generation and export
- **Phase 9**: Testing, optimization, and documentation