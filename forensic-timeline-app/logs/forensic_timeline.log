2025-07-01 17:23:19,190 INFO: Forensic Timeline Analysis startup [in /home/<USER>/TFTPCAP/forensic-timeline-app/app/__init__.py:33]
2025-07-01 21:52:01,323 INFO: Forensic Timeline Analysis startup [in /home/<USER>/TFTPCAP/forensic-timeline-app/app/__init__.py:33]
2025-07-01 22:56:30,721 INFO: Forensic Timeline Analysis startup [in /home/<USER>/TFTPCAP/forensic-timeline-app/app/__init__.py:33]
2025-07-01 23:08:55,193 INFO: Forensic Timeline Analysis startup [in /home/<USER>/TFTPCAP/forensic-timeline-app/app/__init__.py:33]
2025-07-01 23:20:33,818 ERROR: Exception on /report/e3504a93-ca8a-4a3e-9c05-82a1cc262cde [GET] [in /home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/app/main.py", line 155, in report_page
    return render_template('report.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/flask/templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/jinja2/environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/jinja2/environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/jinja2/environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/jinja2/loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/flask/templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/TFTPCAP/forensic-timeline-app/test_env/lib/python3.12/site-packages/flask/templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: report.html
2025-07-01 23:23:34,925 INFO: Forensic Timeline Analysis startup [in /home/<USER>/TFTPCAP/forensic-timeline-app/app/__init__.py:33]
