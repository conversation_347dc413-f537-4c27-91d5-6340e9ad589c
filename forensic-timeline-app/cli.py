#!/usr/bin/env python3
"""
Command-line interface for the Forensic Timeline Analysis application.
"""

import argparse
import sys
import os
import json
from datetime import datetime
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.config import Config
from app.models.event import EventCategory
from app.analyzers.network_analyzer import NetworkAnalyzer

def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Forensic Timeline Analysis - Offline PCAP and Log Analysis",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze a single PCAP file
  python cli.py analyze --pcap attack.pcap --category attack --output results/

  # Analyze baseline and attack PCAPs
  python cli.py analyze --baseline-pcap baseline.pcap --attack-pcap attack.pcap --output results/

  # Test network analysis tools
  python cli.py test-tools
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Analyze command (enhanced)
    analyze_parser = subparsers.add_parser('analyze', help='Complete forensic analysis')
    analyze_parser.add_argument('--pcap', help='Single PCAP file to analyze')
    analyze_parser.add_argument('--baseline-pcap', help='Baseline PCAP file(s)', nargs='*')
    analyze_parser.add_argument('--attack-pcap', help='Attack PCAP file(s)', nargs='*')
    analyze_parser.add_argument('--baseline-logs', help='Baseline log files', nargs='*')
    analyze_parser.add_argument('--attack-logs', help='Attack log files', nargs='*')
    analyze_parser.add_argument('--category', choices=['baseline', 'attack'],
                               help='Category for single PCAP analysis')
    analyze_parser.add_argument('--output', '-o', required=True,
                               help='Output directory for results')
    analyze_parser.add_argument('--format', choices=['json', 'csv', 'html'],
                               default='json', help='Output format')
    analyze_parser.add_argument('--correlate', action='store_true',
                               help='Enable event correlation analysis')
    analyze_parser.add_argument('--session-id', help='Analysis session ID')
    analyze_parser.add_argument('--verbose', '-v', action='store_true',
                               help='Enable verbose logging')

    # Parse logs command
    parse_parser = subparsers.add_parser('parse-logs', help='Parse log files only')
    parse_parser.add_argument('--input', '-i', required=True, help='Input log file or directory')
    parse_parser.add_argument('--category', choices=['baseline', 'attack'], required=True,
                             help='Event category')
    parse_parser.add_argument('--file-type', choices=['auto', 'evtx', 'syslog', 'c2'],
                             default='auto', help='Log file type')
    parse_parser.add_argument('--output', '-o', required=True, help='Output JSON file')
    parse_parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')

    # Correlate command
    correlate_parser = subparsers.add_parser('correlate', help='Find event correlations')
    correlate_parser.add_argument('--events', '-e', required=True, help='Input events JSON file')
    correlate_parser.add_argument('--output', '-o', required=True, help='Output correlations file')
    correlate_parser.add_argument('--threshold', type=float, default=0.3,
                                 help='Correlation threshold (0.0-1.0)')
    correlate_parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')

    # Compare command (baseline vs attack)
    compare_parser = subparsers.add_parser('compare', help='Compare baseline vs attack scenarios')
    compare_parser.add_argument('--baseline-events', required=True, help='Baseline events JSON file')
    compare_parser.add_argument('--attack-events', required=True, help='Attack events JSON file')
    compare_parser.add_argument('--output', '-o', required=True, help='Output comparison file')
    compare_parser.add_argument('--filter-baseline', action='store_true',
                               help='Apply baseline filtering to suppress common events')
    compare_parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')

    # Report command
    report_parser = subparsers.add_parser('report', help='Generate comprehensive analysis report')
    report_parser.add_argument('--timeline', '-t', required=True, help='Timeline JSON file')
    report_parser.add_argument('--correlations', '-c', help='Correlations JSON file')
    report_parser.add_argument('--comparison', help='Comparison results JSON file')
    report_parser.add_argument('--output', '-o', required=True, help='Output report file')
    report_parser.add_argument('--format', choices=['html', 'json', 'markdown'],
                               default='html', help='Report format')
    report_parser.add_argument('--template', help='Custom report template')
    report_parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')

    # Test tools command
    test_parser = subparsers.add_parser('test-tools', help='Test Suricata and Zeek installation')
    test_parser.add_argument('--verbose', '-v', action='store_true',
                           help='Enable verbose logging')

    # Version command
    version_parser = subparsers.add_parser('version', help='Show version information')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 1

    # Configure logging
    import logging
    log_level = logging.DEBUG if getattr(args, 'verbose', False) else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Execute command
    if args.command == 'analyze':
        return analyze_command(args)
    elif args.command == 'parse-logs':
        return parse_logs_command(args)
    elif args.command == 'correlate':
        return correlate_command(args)
    elif args.command == 'compare':
        return compare_command(args)
    elif args.command == 'report':
        return report_command(args)
    elif args.command == 'test-tools':
        return test_tools_command(args)
    elif args.command == 'version':
        return version_command(args)
    else:
        parser.print_help()
        return 1

def analyze_command(args):
    """Execute the enhanced analyze command with log parsing and correlation."""
    import uuid
    from app.parsers.log_normalizer import LogNormalizer
    from app.models.timeline_merger import TimelineMerger

    try:
        print("🚀 Starting Comprehensive Forensic Timeline Analysis")
        print("=" * 60)

        # Validate inputs
        has_pcap = args.pcap or args.baseline_pcap or args.attack_pcap
        has_logs = args.baseline_logs or args.attack_logs

        if not has_pcap and not has_logs:
            print("❌ Error: At least one input file (PCAP or logs) must be specified")
            return 1

        # Create output directory
        output_dir = Path(args.output)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Generate session ID
        session_id = args.session_id or str(uuid.uuid4())

        print(f"📁 Output directory: {output_dir}")
        print(f"📊 Output format: {args.format}")
        print(f"🆔 Session ID: {session_id}")
        print()

        all_baseline_events = []
        all_attack_events = []

        # Phase 1: Analyze PCAP files
        if has_pcap:
            print("📡 Phase 1: Analyzing Network Traffic")
            config = Config()
            analyzer = NetworkAnalyzer(config)

            # Handle single PCAP with category
            if args.pcap:
                if not args.category:
                    print("❌ Error: --category is required when using --pcap")
                    return 1

                if not os.path.exists(args.pcap):
                    print(f"❌ Error: PCAP file not found: {args.pcap}")
                    return 1

                print(f"  📊 Processing {args.category} PCAP: {args.pcap}")
                events = analyzer.analyze_pcap(args.pcap, EventCategory(args.category), str(output_dir))

                if args.category == 'baseline':
                    all_baseline_events.extend(events)
                else:
                    all_attack_events.extend(events)

                print(f"    ✅ Extracted {len(events)} network events")

            # Handle baseline PCAPs
            if args.baseline_pcap:
                for pcap_file in args.baseline_pcap:
                    if not os.path.exists(pcap_file):
                        print(f"❌ Error: Baseline PCAP not found: {pcap_file}")
                        return 1

                    print(f"  📊 Processing baseline PCAP: {pcap_file}")
                    events = analyzer.analyze_pcap(pcap_file, EventCategory.BASELINE, str(output_dir))
                    all_baseline_events.extend(events)
                    print(f"    ✅ Extracted {len(events)} network events")

            # Handle attack PCAPs
            if args.attack_pcap:
                for pcap_file in args.attack_pcap:
                    if not os.path.exists(pcap_file):
                        print(f"❌ Error: Attack PCAP not found: {pcap_file}")
                        return 1

                    print(f"  🚨 Processing attack PCAP: {pcap_file}")
                    events = analyzer.analyze_pcap(pcap_file, EventCategory.ATTACK, str(output_dir))
                    all_attack_events.extend(events)
                    print(f"    ✅ Extracted {len(events)} network events")

            total_network_events = len(all_baseline_events) + len(all_attack_events)
            print(f"  📈 Total network events: {total_network_events}")
            print()

        # Phase 2: Parse log files
        if has_logs:
            print("📝 Phase 2: Parsing Log Files")
            log_normalizer = LogNormalizer()

            # Parse baseline logs
            if args.baseline_logs:
                for log_file in args.baseline_logs:
                    if not os.path.exists(log_file):
                        print(f"❌ Error: Baseline log not found: {log_file}")
                        return 1

                baseline_files = [{'filepath': f, 'file_type': 'auto'} for f in args.baseline_logs]
                baseline_log_events = log_normalizer.parse_log_files(baseline_files, EventCategory.BASELINE)
                all_baseline_events.extend(baseline_log_events)
                print(f"  ✅ Parsed {len(baseline_log_events)} baseline log events")

            # Parse attack logs
            if args.attack_logs:
                for log_file in args.attack_logs:
                    if not os.path.exists(log_file):
                        print(f"❌ Error: Attack log not found: {log_file}")
                        return 1

                attack_files = [{'filepath': f, 'file_type': 'auto'} for f in args.attack_logs]
                attack_log_events = log_normalizer.parse_log_files(attack_files, EventCategory.ATTACK)
                all_attack_events.extend(attack_log_events)
                print(f"  ✅ Parsed {len(attack_log_events)} attack log events")

            total_log_events = len([e for e in all_baseline_events + all_attack_events if e.source.value.endswith('_log') or e.source.value.endswith('_syslog')])
            print(f"  📈 Total log events: {total_log_events}")
            print()

        # Phase 3: Merge and correlate events
        print("🔗 Phase 3: Event Correlation and Timeline Generation")

        merger = TimelineMerger()
        timeline, correlations = merger.merge_timelines(all_baseline_events, all_attack_events, session_id)

        print(f"  ✅ Created unified timeline with {len(timeline.events)} events")
        print(f"  ✅ Found {len(correlations)} event correlations")
        print()

        # Phase 4: Baseline vs Attack Comparison (if both scenarios present)
        comparison_result = None
        if all_baseline_events and all_attack_events:
            print("🔍 Phase 4: Baseline vs Attack Comparison")
            from app.analyzers.comparison_engine import BaselineAttackComparison

            comparison_engine = BaselineAttackComparison()
            comparison_result = comparison_engine.compare_scenarios(all_baseline_events, all_attack_events)

            print(f"  ✅ Risk Score: {comparison_result.comparison_summary['risk_score']:.1f}/100")
            print(f"  ✅ Risk Level: {comparison_result.comparison_summary['risk_level']}")
            print(f"  ✅ Unique Attack Indicators: {sum(len(v) for v in comparison_result.unique_attack_indicators.values())}")
            print()

        # Phase 5: Generate outputs
        print("📊 Phase 5: Generating Analysis Outputs")

        # Save timeline data
        timeline_file = output_dir / "timeline.json"
        timeline_data = {
            'session_id': session_id,
            'created_at': datetime.utcnow().isoformat(),
            'total_events': len(timeline.events),
            'events': [event.to_dict() for event in timeline.events]
        }

        with open(timeline_file, 'w') as f:
            json.dump(timeline_data, f, indent=2, default=str)
        print(f"  ✅ Timeline saved: {timeline_file}")

        # Save correlations if enabled
        if args.correlate or correlations:
            correlations_file = output_dir / "correlations.json"
            correlations_data = {
                'session_id': session_id,
                'total_correlations': len(correlations),
                'correlations': [corr.to_dict() for corr in correlations]
            }

            with open(correlations_file, 'w') as f:
                json.dump(correlations_data, f, indent=2, default=str)
            print(f"  ✅ Correlations saved: {correlations_file}")

        # Save comparison results if available
        if comparison_result:
            comparison_file = output_dir / "comparison.json"
            comparison_data = {
                'session_id': session_id,
                'comparison_results': comparison_result.to_dict()
            }

            with open(comparison_file, 'w') as f:
                json.dump(comparison_data, f, indent=2, default=str)
            print(f"  ✅ Comparison saved: {comparison_file}")

        # Generate analysis summary
        summary_file = output_dir / "analysis_summary.json"
        summary_data = generate_analysis_summary(timeline, correlations, session_id)

        with open(summary_file, 'w') as f:
            json.dump(summary_data, f, indent=2, default=str)
        print(f"  ✅ Summary saved: {summary_file}")

        # Generate comprehensive report
        if args.format == 'html' or comparison_result:
            print("📋 Generating comprehensive report...")
            from app.reports.report_generator import ReportGenerator

            report_generator = ReportGenerator()
            report_file = output_dir / f"forensic_report.{args.format}"

            report_generator.generate_comprehensive_report(
                timeline=timeline,
                correlations=correlations,
                comparison_result=comparison_result,
                output_path=str(report_file),
                format_type=args.format
            )
            print(f"  ✅ Report saved: {report_file}")

        print()
        print("🎉 Analysis Complete!")
        print("=" * 60)
        print(f"📁 Results saved to: {output_dir}")
        print(f"📊 Total events processed: {len(timeline.events)}")
        print(f"🔗 Correlations found: {len(correlations)}")
        print(f"🆔 Session ID: {session_id}")

        return 0

    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

def parse_logs_command(args):
    """Execute the parse-logs command."""
    from app.parsers.log_normalizer import LogNormalizer

    try:
        print("📝 Starting Log Parsing")
        print("=" * 40)

        # Validate input
        if not os.path.exists(args.input):
            print(f"❌ Error: Input not found: {args.input}")
            return 1

        # Determine if input is file or directory
        input_path = Path(args.input)
        log_files = []

        if input_path.is_file():
            log_files = [{'filepath': str(input_path), 'file_type': args.file_type}]
        elif input_path.is_dir():
            # Find all log files in directory
            extensions = ['.log', '.txt', '.evtx', '.csv', '.json']
            for ext in extensions:
                log_files.extend([
                    {'filepath': str(f), 'file_type': args.file_type}
                    for f in input_path.glob(f'*{ext}')
                ])

        if not log_files:
            print(f"❌ Error: No log files found in {args.input}")
            return 1

        print(f"📁 Input: {args.input}")
        print(f"📊 Category: {args.category}")
        print(f"📄 Files to process: {len(log_files)}")
        print()

        # Parse logs
        log_normalizer = LogNormalizer()
        category = EventCategory.BASELINE if args.category == 'baseline' else EventCategory.ATTACK
        events = log_normalizer.parse_log_files(log_files, category)

        print(f"✅ Parsed {len(events)} events")

        # Save results
        output_data = {
            'input_path': str(input_path),
            'category': args.category,
            'file_type': args.file_type,
            'total_events': len(events),
            'events': [event.to_dict() for event in events]
        }

        with open(args.output, 'w') as f:
            json.dump(output_data, f, indent=2, default=str)

        print(f"💾 Results saved to: {args.output}")
        return 0

    except Exception as e:
        print(f"❌ Error during log parsing: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

def correlate_command(args):
    """Execute the correlate command."""
    from app.models.timeline import Timeline
    from app.models.correlation import EventCorrelationEngine
    from app.models.event import Event

    try:
        print("🔗 Starting Event Correlation Analysis")
        print("=" * 45)

        # Load events
        if not os.path.exists(args.events):
            print(f"❌ Error: Events file not found: {args.events}")
            return 1

        print(f"📄 Loading events from: {args.events}")

        with open(args.events, 'r') as f:
            events_data = json.load(f)

        # Convert to Event objects
        events = []
        if 'events' in events_data:
            events_list = events_data['events']
        else:
            events_list = events_data

        for event_dict in events_list:
            try:
                event = Event.from_dict(event_dict)
                events.append(event)
            except Exception as e:
                if args.verbose:
                    print(f"⚠️  Skipping invalid event: {e}")
                continue

        print(f"✅ Loaded {len(events)} events")
        print(f"🎯 Correlation threshold: {args.threshold}")
        print()

        # Create timeline and find correlations
        timeline = Timeline(session_id="correlation_analysis")
        timeline.add_events(events)

        correlation_engine = EventCorrelationEngine()
        correlation_engine.correlation_threshold = args.threshold

        correlations = correlation_engine.correlate_timeline(timeline)

        print(f"🔍 Found {len(correlations)} correlations")

        # Generate correlation summary
        summary = correlation_engine.generate_correlation_summary(correlations)

        # Save results
        output_data = {
            'input_file': args.events,
            'threshold': args.threshold,
            'total_events': len(events),
            'total_correlations': len(correlations),
            'summary': summary,
            'correlations': [corr.to_dict() for corr in correlations]
        }

        with open(args.output, 'w') as f:
            json.dump(output_data, f, indent=2, default=str)

        print(f"💾 Correlations saved to: {args.output}")
        print(f"📊 Average correlation score: {summary['average_score']:.3f}")
        print(f"🏆 Top correlation score: {summary['max_score']:.3f}")

        return 0

    except Exception as e:
        print(f"❌ Error during correlation analysis: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

def compare_command(args):
    """Execute the compare command for baseline vs attack analysis."""
    from app.analyzers.comparison_engine import BaselineAttackComparison
    from app.analyzers.baseline_filter import BaselineFilter
    from app.models.event import Event

    try:
        print("🔍 Starting Baseline vs Attack Comparison")
        print("=" * 50)

        # Load baseline events
        if not os.path.exists(args.baseline_events):
            print(f"❌ Error: Baseline events file not found: {args.baseline_events}")
            return 1

        print(f"📄 Loading baseline events from: {args.baseline_events}")
        with open(args.baseline_events, 'r') as f:
            baseline_data = json.load(f)

        # Load attack events
        if not os.path.exists(args.attack_events):
            print(f"❌ Error: Attack events file not found: {args.attack_events}")
            return 1

        print(f"📄 Loading attack events from: {args.attack_events}")
        with open(args.attack_events, 'r') as f:
            attack_data = json.load(f)

        # Convert to Event objects
        baseline_events = []
        attack_events = []

        # Handle different JSON structures
        baseline_list = baseline_data.get('events', baseline_data) if isinstance(baseline_data, dict) else baseline_data
        attack_list = attack_data.get('events', attack_data) if isinstance(attack_data, dict) else attack_data

        for event_dict in baseline_list:
            try:
                event = Event.from_dict(event_dict)
                baseline_events.append(event)
            except Exception as e:
                if args.verbose:
                    print(f"⚠️  Skipping invalid baseline event: {e}")

        for event_dict in attack_list:
            try:
                event = Event.from_dict(event_dict)
                attack_events.append(event)
            except Exception as e:
                if args.verbose:
                    print(f"⚠️  Skipping invalid attack event: {e}")

        print(f"✅ Loaded {len(baseline_events)} baseline and {len(attack_events)} attack events")
        print()

        # Apply baseline filtering if requested
        if args.filter_baseline:
            print("🔧 Applying baseline filtering...")
            baseline_filter = BaselineFilter()
            filtered_baseline, filtered_attack = baseline_filter.filter_events(baseline_events, attack_events)

            print(f"  📉 Filtered baseline: {len(baseline_events)} → {len(filtered_baseline)} events")
            print(f"  📉 Filtered attack: {len(attack_events)} → {len(filtered_attack)} events")

            baseline_events = filtered_baseline
            attack_events = filtered_attack
            print()

        # Perform comparison
        print("🔍 Performing baseline vs attack comparison...")
        comparison_engine = BaselineAttackComparison()
        comparison_result = comparison_engine.compare_scenarios(baseline_events, attack_events)

        print(f"✅ Comparison complete!")
        print(f"  🎯 Risk Score: {comparison_result.comparison_summary['risk_score']:.1f}/100")
        print(f"  ⚠️  Risk Level: {comparison_result.comparison_summary['risk_level']}")
        print(f"  🔍 Unique Attack Indicators: {sum(len(v) for v in comparison_result.unique_attack_indicators.values())}")
        print(f"  🚨 Suspicious Patterns: {len(comparison_result.suspicious_patterns)}")
        print(f"  📊 Timeline Anomalies: {len(comparison_result.timeline_anomalies)}")
        print()

        # Save results
        output_data = {
            'comparison_metadata': {
                'baseline_file': args.baseline_events,
                'attack_file': args.attack_events,
                'filtering_applied': args.filter_baseline,
                'generated_at': datetime.utcnow().isoformat()
            },
            'comparison_results': comparison_result.to_dict()
        }

        with open(args.output, 'w') as f:
            json.dump(output_data, f, indent=2, default=str)

        print(f"💾 Comparison results saved to: {args.output}")

        # Print summary of key findings
        if comparison_result.suspicious_patterns:
            print("\n🚨 Top Suspicious Patterns:")
            for i, pattern in enumerate(comparison_result.suspicious_patterns[:3], 1):
                print(f"  {i}. {pattern['type']}: {pattern['description']}")

        if comparison_result.unique_attack_indicators:
            print("\n🎯 Unique Attack Indicators:")
            for indicator_type, indicators in comparison_result.unique_attack_indicators.items():
                if indicators:
                    print(f"  {indicator_type}: {len(indicators)} unique items")

        return 0

    except Exception as e:
        print(f"❌ Error during comparison: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

def report_command(args):
    """Execute the report command for comprehensive report generation."""
    from app.reports.report_generator import ReportGenerator
    from app.models.timeline import Timeline
    from app.models.correlation import CorrelationResult
    from app.analyzers.comparison_engine import ComparisonResult
    from app.models.event import Event

    try:
        print("📊 Starting Comprehensive Report Generation")
        print("=" * 50)

        # Load timeline
        if not os.path.exists(args.timeline):
            print(f"❌ Error: Timeline file not found: {args.timeline}")
            return 1

        print(f"📄 Loading timeline from: {args.timeline}")
        with open(args.timeline, 'r') as f:
            timeline_data = json.load(f)

        # Create timeline object
        session_id = timeline_data.get('session_id', 'unknown')
        timeline = Timeline(session_id=session_id)

        # Load events
        events_list = timeline_data.get('events', [])
        for event_dict in events_list:
            try:
                event = Event.from_dict(event_dict)
                timeline.add_event(event)
            except Exception as e:
                if args.verbose:
                    print(f"⚠️  Skipping invalid event: {e}")

        print(f"✅ Loaded timeline with {len(timeline.events)} events")

        # Load correlations if provided
        correlations = []
        if args.correlations:
            if not os.path.exists(args.correlations):
                print(f"⚠️  Warning: Correlations file not found: {args.correlations}")
            else:
                print(f"📄 Loading correlations from: {args.correlations}")
                with open(args.correlations, 'r') as f:
                    correlations_data = json.load(f)

                correlations_list = correlations_data.get('correlations', [])
                for corr_dict in correlations_list:
                    try:
                        # Create correlation result from dict
                        correlation = CorrelationResult.from_dict(corr_dict)
                        correlations.append(correlation)
                    except Exception as e:
                        if args.verbose:
                            print(f"⚠️  Skipping invalid correlation: {e}")

                print(f"✅ Loaded {len(correlations)} correlations")

        # Load comparison results if provided
        comparison_result = None
        if args.comparison:
            if not os.path.exists(args.comparison):
                print(f"⚠️  Warning: Comparison file not found: {args.comparison}")
            else:
                print(f"📄 Loading comparison results from: {args.comparison}")
                with open(args.comparison, 'r') as f:
                    comparison_data = json.load(f)

                # Create comparison result object
                comparison_result = ComparisonResult()
                comparison_result.__dict__.update(comparison_data.get('comparison_results', {}))

                print(f"✅ Loaded comparison results")

        print()

        # Generate report
        print(f"📊 Generating {args.format.upper()} report...")
        report_generator = ReportGenerator()

        output_path = report_generator.generate_comprehensive_report(
            timeline=timeline,
            correlations=correlations,
            comparison_result=comparison_result,
            output_path=args.output,
            format_type=args.format
        )

        print(f"✅ Report generated successfully!")
        print(f"📁 Report saved to: {output_path}")

        # Print report summary
        print(f"\n📋 Report Summary:")
        print(f"  📊 Events analyzed: {len(timeline.events)}")
        print(f"  🔗 Correlations: {len(correlations)}")
        print(f"  📝 Format: {args.format.upper()}")
        if comparison_result:
            risk_level = getattr(comparison_result, 'comparison_summary', {}).get('risk_level', 'UNKNOWN')
            print(f"  ⚠️  Risk Level: {risk_level}")

        return 0

    except Exception as e:
        print(f"❌ Error during report generation: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

def generate_analysis_summary(timeline, correlations, session_id):
    """Generate a comprehensive analysis summary."""
    from collections import Counter

    events = timeline.events

    # Event statistics
    source_counts = Counter(e.source.value for e in events)
    category_counts = Counter(e.category.value for e in events)
    severity_counts = Counter(e.severity for e in events if e.severity)
    type_counts = Counter(e.event_type.value for e in events)

    # Time range
    if events:
        start_time = min(e.timestamp for e in events)
        end_time = max(e.timestamp for e in events)
        duration = (end_time - start_time).total_seconds()
    else:
        start_time = end_time = None
        duration = 0

    # Correlation statistics
    correlation_summary = {}
    if correlations:
        scores = [c.score for c in correlations]
        correlation_summary = {
            'total': len(correlations),
            'average_score': sum(scores) / len(scores),
            'max_score': max(scores),
            'min_score': min(scores)
        }

    return {
        'session_id': session_id,
        'generated_at': datetime.utcnow().isoformat(),
        'timeline_summary': {
            'total_events': len(events),
            'start_time': start_time.isoformat() if start_time else None,
            'end_time': end_time.isoformat() if end_time else None,
            'duration_seconds': duration,
            'sources': dict(source_counts),
            'categories': dict(category_counts),
            'severities': dict(severity_counts),
            'event_types': dict(type_counts)
        },
        'correlation_summary': correlation_summary
    }

def test_tools_command(args):
    """Test Suricata and Zeek installation."""
    import subprocess

    print("Testing network analysis tools...")

    config = Config()

    # Test Suricata
    print(f"\nTesting Suricata at {config.SURICATA_PATH}...")
    try:
        result = subprocess.run([config.SURICATA_PATH, '--version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ Suricata is working")
            print(f"  Version: {result.stdout.strip()}")
        else:
            print("✗ Suricata test failed")
            print(f"  Error: {result.stderr}")
    except Exception as e:
        print(f"✗ Suricata test failed: {e}")

    # Test Zeek
    print(f"\nTesting Zeek at {config.ZEEK_PATH}...")
    try:
        result = subprocess.run([config.ZEEK_PATH, '--version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ Zeek is working")
            print(f"  Version: {result.stdout.strip()}")
        else:
            print("✗ Zeek test failed")
            print(f"  Error: {result.stderr}")
    except Exception as e:
        print(f"✗ Zeek test failed: {e}")

    # Test configuration files
    print(f"\nTesting configuration files...")
    if os.path.exists(config.SURICATA_CONFIG):
        print(f"✓ Suricata config found: {config.SURICATA_CONFIG}")
    else:
        print(f"✗ Suricata config missing: {config.SURICATA_CONFIG}")

    if os.path.exists(config.SURICATA_RULES):
        print(f"✓ Suricata rules found: {config.SURICATA_RULES}")
    else:
        print(f"✗ Suricata rules missing: {config.SURICATA_RULES}")

    return 0

def version_command(args):
    """Show version information."""
    print("Forensic Timeline Analysis v1.0.0")
    print("Containerized forensic analysis tool for network traffic and system logs")
    print("\nComponents:")
    print("  - Suricata IDS for network intrusion detection")
    print("  - Zeek for comprehensive network analysis")
    print("  - Multi-source event correlation")
    print("  - Interactive timeline visualization")
    return 0

if __name__ == '__main__':
    sys.exit(main())