#!/usr/bin/env python3
"""
Command-line interface for the Forensic Timeline Analysis application.
"""

import argparse
import sys
import os
import json
from datetime import datetime
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.config import Config
from app.models.event import EventCategory
from app.analyzers.network_analyzer import NetworkAnalyzer

def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Forensic Timeline Analysis - Offline PCAP and Log Analysis",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze a single PCAP file
  python cli.py analyze --pcap attack.pcap --category attack --output results/

  # Analyze baseline and attack PCAPs
  python cli.py analyze --baseline-pcap baseline.pcap --attack-pcap attack.pcap --output results/

  # Test network analysis tools
  python cli.py test-tools
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze PCAP files')
    analyze_parser.add_argument('--pcap', help='Single PCAP file to analyze')
    analyze_parser.add_argument('--baseline-pcap', help='Baseline PCAP file')
    analyze_parser.add_argument('--attack-pcap', help='Attack PCAP file')
    analyze_parser.add_argument('--category', choices=['baseline', 'attack'],
                               help='Category for single PCAP analysis')
    analyze_parser.add_argument('--output', '-o', required=True,
                               help='Output directory for results')
    analyze_parser.add_argument('--verbose', '-v', action='store_true',
                               help='Enable verbose logging')

    # Test tools command
    test_parser = subparsers.add_parser('test-tools', help='Test Suricata and Zeek installation')
    test_parser.add_argument('--verbose', '-v', action='store_true',
                           help='Enable verbose logging')

    # Version command
    version_parser = subparsers.add_parser('version', help='Show version information')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 1

    # Configure logging
    import logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Execute command
    if args.command == 'analyze':
        return analyze_command(args)
    elif args.command == 'test-tools':
        return test_tools_command(args)
    elif args.command == 'version':
        return version_command(args)
    else:
        parser.print_help()
        return 1

def analyze_command(args):
    """Execute the analyze command."""
    try:
        # Validate arguments
        if args.pcap:
            if not args.category:
                print("Error: --category is required when using --pcap")
                return 1
            pcap_files = [(args.pcap, EventCategory(args.category))]
        elif args.baseline_pcap and args.attack_pcap:
            pcap_files = [
                (args.baseline_pcap, EventCategory.BASELINE),
                (args.attack_pcap, EventCategory.ATTACK)
            ]
        else:
            print("Error: Specify either --pcap with --category, or both --baseline-pcap and --attack-pcap")
            return 1

        # Validate PCAP files exist
        for pcap_path, _ in pcap_files:
            if not os.path.exists(pcap_path):
                print(f"Error: PCAP file not found: {pcap_path}")
                return 1

        # Create output directory
        output_dir = Path(args.output)
        output_dir.mkdir(parents=True, exist_ok=True)

        print("Starting forensic timeline analysis...")
        print(f"Output directory: {output_dir}")

        # Initialize analyzer
        config = Config()
        analyzer = NetworkAnalyzer(config)

        all_events = []

        # Analyze each PCAP
        for pcap_path, category in pcap_files:
            print(f"\nAnalyzing {category.value} PCAP: {pcap_path}")

            try:
                events = analyzer.analyze_pcap(pcap_path, category, str(output_dir))
                all_events.extend(events)
                print(f"Generated {len(events)} events from {pcap_path}")

            except Exception as e:
                print(f"Error analyzing {pcap_path}: {e}")
                return 1

        # Sort events by timestamp
        all_events.sort(key=lambda e: e.timestamp)

        # Save results
        results_file = output_dir / "timeline_events.json"
        with open(results_file, 'w') as f:
            json.dump([event.to_dict() for event in all_events], f, indent=2)

        print(f"\nAnalysis completed successfully!")
        print(f"Total events: {len(all_events)}")
        print(f"Results saved to: {results_file}")

        # Print summary
        if all_events:
            start_time = min(e.timestamp for e in all_events)
            end_time = max(e.timestamp for e in all_events)
            duration = (end_time - start_time).total_seconds()

            print(f"\nTimeline Summary:")
            print(f"  Start time: {start_time}")
            print(f"  End time: {end_time}")
            print(f"  Duration: {duration:.1f} seconds")

            # Event counts by source
            from collections import Counter
            source_counts = Counter(e.source.value for e in all_events)
            print(f"\nEvents by source:")
            for source, count in source_counts.items():
                print(f"  {source}: {count}")

        return 0

    except Exception as e:
        print(f"Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_tools_command(args):
    """Test Suricata and Zeek installation."""
    import subprocess

    print("Testing network analysis tools...")

    config = Config()

    # Test Suricata
    print(f"\nTesting Suricata at {config.SURICATA_PATH}...")
    try:
        result = subprocess.run([config.SURICATA_PATH, '--version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ Suricata is working")
            print(f"  Version: {result.stdout.strip()}")
        else:
            print("✗ Suricata test failed")
            print(f"  Error: {result.stderr}")
    except Exception as e:
        print(f"✗ Suricata test failed: {e}")

    # Test Zeek
    print(f"\nTesting Zeek at {config.ZEEK_PATH}...")
    try:
        result = subprocess.run([config.ZEEK_PATH, '--version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ Zeek is working")
            print(f"  Version: {result.stdout.strip()}")
        else:
            print("✗ Zeek test failed")
            print(f"  Error: {result.stderr}")
    except Exception as e:
        print(f"✗ Zeek test failed: {e}")

    # Test configuration files
    print(f"\nTesting configuration files...")
    if os.path.exists(config.SURICATA_CONFIG):
        print(f"✓ Suricata config found: {config.SURICATA_CONFIG}")
    else:
        print(f"✗ Suricata config missing: {config.SURICATA_CONFIG}")

    if os.path.exists(config.SURICATA_RULES):
        print(f"✓ Suricata rules found: {config.SURICATA_RULES}")
    else:
        print(f"✗ Suricata rules missing: {config.SURICATA_RULES}")

    return 0

def version_command(args):
    """Show version information."""
    print("Forensic Timeline Analysis v1.0.0")
    print("Containerized forensic analysis tool for network traffic and system logs")
    print("\nComponents:")
    print("  - Suricata IDS for network intrusion detection")
    print("  - Zeek for comprehensive network analysis")
    print("  - Multi-source event correlation")
    print("  - Interactive timeline visualization")
    return 0

if __name__ == '__main__':
    sys.exit(main())