#!/usr/bin/env python3
"""
Test script for the event correlation engine.
"""

import sys
import os
from datetime import datetime, timedelta

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_correlation_engine():
    """Test the correlation engine with sample events."""
    print("🔍 Testing Event Correlation Engine...")

    try:
        from app.models.event import Event, EventType, EventSource, EventCategory
        from app.models.timeline import Timeline
        from app.models.correlation import EventCorrelationEngine
        from app.models.timeline_merger import TimelineMerger

        # Create sample events that should correlate
        base_time = datetime(2023, 1, 15, 14, 30, 0)

        # SSH login event
        ssh_event = Event(
            timestamp=base_time,
            source=EventSource.LINUX_SYSLOG,
            event_type=EventType.AUTHENTICATION,
            category=EventCategory.ATTACK,
            description="SSH successful login: admin from ********",
            metadata={
                'username': 'admin',
                'src_ip': '********',
                'dst_ip': '*************'
            }
        )

        # Process creation event (30 seconds later)
        process_event = Event(
            timestamp=base_time + timedelta(seconds=30),
            source=EventSource.LINUX_SYSLOG,
            event_type=EventType.PROCESS_CREATION,
            category=EventCategory.ATTACK,
            description="Sudo execution: admin -> root: /bin/cat /etc/passwd",
            metadata={
                'username': 'admin',
                'process_name': 'cat',
                'command': '/bin/cat /etc/passwd'
            }
        )

        # C2 command event (1 minute later)
        c2_event = Event(
            timestamp=base_time + timedelta(minutes=1),
            source=EventSource.C2_LOG,
            event_type=EventType.C2_COMMAND,
            category=EventCategory.ATTACK,
            description="C2 command: execute whoami",
            metadata={
                'agent_id': 'agent001',
                'target': '*************',
                'command': 'whoami'
            }
        )

        # Network connection event (2 minutes later)
        network_event = Event(
            timestamp=base_time + timedelta(minutes=2),
            source=EventSource.SURICATA,
            event_type=EventType.NETWORK_CONNECTION,
            category=EventCategory.ATTACK,
            description="Outbound connection to C2 server",
            metadata={
                'src_ip': '*************',
                'dst_ip': '********',
                'dst_port': '443',
                'protocol': 'TCP'
            }
        )

        # Unrelated event (different IP, much later)
        unrelated_event = Event(
            timestamp=base_time + timedelta(hours=2),
            source=EventSource.LINUX_SYSLOG,
            event_type=EventType.SYSTEM_LOG,
            category=EventCategory.BASELINE,
            description="System backup completed",
            metadata={
                'src_ip': '************',
                'process_name': 'backup'
            }
        )

        events = [ssh_event, process_event, c2_event, network_event, unrelated_event]

        # Test correlation engine
        correlation_engine = EventCorrelationEngine()

        # Create timeline
        timeline = Timeline(session_id="test_session")
        timeline.add_events(events)

        # Find correlations
        correlations = correlation_engine.correlate_timeline(timeline)

        print(f"✅ Found {len(correlations)} correlations")

        # Display correlations
        for i, correlation in enumerate(correlations, 1):
            print(f"\n  Correlation {i}:")
            print(f"    Score: {correlation.score:.3f}")
            print(f"    Rules: {', '.join(correlation.rules_matched)}")
            print(f"    Event 1: {correlation.event1.description[:50]}...")
            print(f"    Event 2: {correlation.event2.description[:50]}...")
            print(f"    Time diff: {correlation.to_dict()['time_difference_seconds']:.0f} seconds")

        # Test correlation summary
        summary = correlation_engine.generate_correlation_summary(correlations)
        print(f"\n📊 Correlation Summary:")
        print(f"  Total correlations: {summary['total_correlations']}")
        print(f"  Average score: {summary['average_score']:.3f}")
        print(f"  Rule frequency: {summary['rules_frequency']}")

        return True

    except Exception as e:
        print(f"❌ Correlation engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_timeline_merger():
    """Test the timeline merger functionality."""
    print("\n🔗 Testing Timeline Merger...")

    try:
        from app.models.event import Event, EventType, EventSource, EventCategory
        from app.models.timeline_merger import TimelineMerger

        # Create baseline events
        baseline_events = []
        base_time = datetime(2023, 1, 15, 8, 0, 0)

        for i in range(5):
            event = Event(
                timestamp=base_time + timedelta(hours=i),
                source=EventSource.LINUX_SYSLOG,
                event_type=EventType.AUTHENTICATION,
                category=EventCategory.BASELINE,
                description=f"Normal SSH login: admin from ************",
                metadata={'username': 'admin', 'src_ip': '************'}
            )
            baseline_events.append(event)

        # Create attack events
        attack_events = []
        attack_time = datetime(2023, 1, 15, 14, 0, 0)

        for i in range(3):
            event = Event(
                timestamp=attack_time + timedelta(minutes=i*10),
                source=EventSource.C2_LOG,
                event_type=EventType.C2_COMMAND,
                category=EventCategory.ATTACK,
                description=f"C2 command {i+1}: malicious activity",
                metadata={'agent_id': 'agent001', 'target': '*************'}
            )
            attack_events.append(event)

        # Test timeline merger
        merger = TimelineMerger()

        # Merge timelines
        timeline, correlations = merger.merge_timelines(
            baseline_events, attack_events, "test_merge_session"
        )

        print(f"✅ Merged timeline created with {len(timeline.events)} events")
        print(f"✅ Found {len(correlations)} correlations in merged timeline")

        # Test time sync detection
        all_events = baseline_events + attack_events
        sync_analysis = merger.detect_time_sync_issues(all_events)

        print(f"✅ Time sync analysis completed")
        print(f"  Sources analyzed: {len(sync_analysis['source_time_ranges'])}")
        print(f"  Sync issues detected: {sync_analysis['has_sync_issues']}")

        # Test attack-focused timeline
        focused_timeline, focused_correlations = merger.create_attack_focused_timeline(
            baseline_events, attack_events, "test_focused_session"
        )

        print(f"✅ Attack-focused timeline created with {len(focused_timeline.events)} events")

        return True

    except Exception as e:
        print(f"❌ Timeline merger test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all correlation tests."""
    print("🚀 Starting Event Correlation Tests")
    print("=" * 50)

    tests = [
        test_correlation_engine,
        test_timeline_merger
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1

    print("\n" + "=" * 50)
    print(f"📊 Correlation Test Results: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 All correlation tests passed! Event correlation engine is working.")
        return 0
    else:
        print("⚠️  Some correlation tests failed. Please check the implementation.")
        return 1

if __name__ == '__main__':
    sys.exit(main())