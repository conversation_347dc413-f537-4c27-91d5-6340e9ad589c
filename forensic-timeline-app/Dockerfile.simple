# Forensic Timeline Analysis - Simplified Docker Container
FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=run.py
ENV FLASK_ENV=production

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    net-tools \
    tcpdump \
    tshark \
    && rm -rf /var/lib/apt/lists/*

# Create app user for security
RUN useradd -m -u 1000 forensic && \
    mkdir -p /app /data /uploads /results /logs && \
    chown -R forensic:forensic /app /data /uploads /results /logs

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY docker/requirements.txt .

# Install Python dependencies
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy application code
COPY --chown=forensic:forensic . .

# Create necessary directories and set permissions
RUN mkdir -p uploads results logs test_data && \
    chown -R forensic:forensic /app

# Switch to non-root user
USER forensic

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1

# Run the application
CMD ["python3", "run.py"]
