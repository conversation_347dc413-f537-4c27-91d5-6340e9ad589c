version: '3.8'

services:
  forensic-timeline:
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: forensic-timeline-analysis
    ports:
      - "5000:5000"
    volumes:
      # Persistent data storage
      - forensic_data:/data
      - forensic_uploads:/app/uploads
      - forensic_results:/app/results
      - forensic_logs:/app/logs
      # Optional: Mount local directory for development
      # - ./uploads:/app/uploads
      # - ./results:/app/results
    environment:
      - FLASK_ENV=production
      - FLASK_APP=run.py
      - PYTHONUNBUFFERED=1
      - TZ=UTC
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - forensic-network

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: forensic-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
    depends_on:
      - forensic-timeline
    restart: unless-stopped
    networks:
      - forensic-network
    profiles:
      - production

volumes:
  forensic_data:
    driver: local
  forensic_uploads:
    driver: local
  forensic_results:
    driver: local
  forensic_logs:
    driver: local

networks:
  forensic-network:
    driver: bridge
