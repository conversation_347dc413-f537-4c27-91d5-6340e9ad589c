<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forensic Analysis Report - 166abe6e-b826-4659-9fcf-34ef4112739c</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #3498db; }
        .threat-level { font-size: 1.2em; font-weight: bold; }
        .critical { color: #e74c3c; }
        .high { color: #f39c12; }
        .medium { color: #f1c40f; }
        .low { color: #27ae60; }
        .data-section { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Forensic Timeline Analysis Report</h1>
        <p>Session ID: 166abe6e-b826-4659-9fcf-34ef4112739c</p>
        <p>Generated: 2025-07-02T01:59:38.486587</p>
    </div>
    
    <div class="section">
        <h2>Executive Summary</h2>
        <div class="data-section">
            <p><strong>Total Events Analyzed:</strong> 14</p>
            <p><strong>Threat Level:</strong> <span class="threat-level LOW">LOW</span></p>
            <p><strong>Correlations Found:</strong> 0</p>
            <p><strong>Analysis Confidence:</strong> VERY LOW</p>
        </div>
        <h3>Key Findings</h3>
        <div class="data-section">
            • Analyzed 14 attack-related events<br>• Overall risk assessment: LOW<br>• Found 8 unique attack indicators
        </div>
    </div>
    
    <div class="section">
        <h2>Detailed Analysis Data</h2>
        <pre>{
  "metadata": {
    "generated_at": "2025-07-02T01:59:38.486587",
    "session_id": "166abe6e-b826-4659-9fcf-34ef4112739c",
    "report_version": "1.0",
    "total_events": 14,
    "analysis_duration_seconds": 1575.0
  },
  "executive_summary": {
    "key_findings": [
      "Analyzed 14 attack-related events",
      "Overall risk assessment: LOW",
      "Found 8 unique attack indicators"
    ],
    "threat_level": "LOW",
    "events_analyzed": 14,
    "correlations_found": 0,
    "analysis_confidence": "VERY LOW"
  },
  "timeline_overview": {
    "start_time": "2023-01-15T14:15:45+00:00",
    "end_time": "2023-01-15T14:42:00+00:00",
    "duration_seconds": 1575.0,
    "total_events": 14,
    "baseline_events": 0,
    "attack_events": 14
  },
  "event_analysis": {
    "source_distribution": {
      "c2_log": 14
    },
    "type_distribution": {
      "c2_command": 14
    },
    "severity_distribution": {
      "low": 5,
      "medium": 3,
      "high": 6
    },
    "timeline_patterns": {
      "hourly_distribution": {
        "14": 14
      },
      "daily_distribution": {
        "2023-01-15": 14
      },
      "peak_hour": {
        "hour": 14,
        "count": 14
      },
      "peak_day": {
        "date": "2023-01-15",
        "count": 14
      },
      "total_days_analyzed": 1
    }
  },
  "correlation_analysis": {
    "total": 0,
    "patterns": {}
  },
  "high_severity_events": [
    {
      "timestamp": "2023-01-15T14:16:00+00:00",
      "source": "c2_log",
      "event_type": "c2_command",
      "category": "attack",
      "severity": "high",
      "description": "C2 command: execute whoami (Agent: agent_001) (Target: *************)",
      "metadata": {
        "command": "execute whoami",
        "agent_id": "agent_001",
        "target": "*************",
        "activity_type": "command",
        "raw_entry": {
          "timestamp": "2023-01-15T14:16:00Z",
          "agent_id": "agent_001",
          "command": "execute whoami",
          "target": "*************",
          "status": "success",
          "response": "admin"
        },
        "status": "success",
        "response": "admin"
      }
    },
    {
      "timestamp": "2023-01-15T14:17:30+00:00",
      "source": "c2_log",
      "event_type": "c2_command",
      "category": "attack",
      "severity": "high",
      "description": "C2 command: execute powershell.exe -c Get-Process (Agent: agent_001) (Target: *************)",
      "metadata": {
        "command": "execute powershell.exe -c Get-Process",
        "agent_id": "agent_001",
        "target": "*************",
        "activity_type": "command",
        "raw_entry": {
          "timestamp": "2023-01-15T14:17:30Z",
          "agent_id": "agent_001",
          "command": "execute powershell.exe -c Get-Process",
          "target": "*************",
          "status": "success",
          "response_size": 2048
        },
        "status": "success",
        "response_size": 2048
      }
    },
    {
      "timestamp": "2023-01-15T14:30:00+00:00",
      "source": "c2_log",
      "event_type": "c2_command",
      "category": "attack",
      "severity": "high",
      "description": "C2 persistence: persistence install (Agent: agent_001) (Target: *************)",
      "metadata": {
        "command": "persistence install",
        "agent_id": "agent_001",
        "target": "*************",
        "activity_type": "persistence",
        "raw_entry": {
          "timestamp": "2023-01-15T14:30:00Z",
          "agent_id": "agent_001",
          "command": "persistence install",
          "target": "*************",
          "status": "success",
          "method": "registry"
        },
        "status": "success",
        "method": "registry"
      }
    },
    {
      "timestamp": "2023-01-15T14:33:00+00:00",
      "source": "c2_log",
      "event_type": "c2_command",
      "category": "attack",
      "severity": "high",
      "description": "C2 command: screenshot (Agent: agent_001) (Target: *************)",
      "metadata": {
        "command": "screenshot",
        "agent_id": "agent_001",
        "target": "*************",
        "activity_type": "command",
        "raw_row": {
          "timestamp": "2023-01-15 14:33:00",
          "agent_id": "agent_001",
          "command": "screenshot",
          "target": "*************",
          "status": "success",
          "details": "desktop capture"
        }
      }
    },
    {
      "timestamp": "2023-01-15T14:35:00+00:00",
      "source": "c2_log",
      "event_type": "c2_command",
      "category": "attack",
      "severity": "high",
      "description": "C2 lateral_movement: lateral_movement scan (Agent: agent_001) (Target: ***********/24)",
      "metadata": {
        "command": "lateral_movement scan",
        "agent_id": "agent_001",
        "target": "***********/24",
        "activity_type": "lateral_movement",
        "raw_entry": {
          "timestamp": "2023-01-15T14:35:00Z",
          "agent_id": "agent_001",
          "command": "lateral_movement scan",
          "target": "***********/24",
          "status": "success",
          "hosts_found": 15
        },
        "status": "success",
        "hosts_found": 15
      }
    },
    {
      "timestamp": "2023-01-15T14:42:00+00:00",
      "source": "c2_log",
      "event_type": "c2_command",
      "category": "attack",
      "severity": "high",
      "description": "C2 exfiltration: exfiltration start (Agent: agent_001) (Target: *************)",
      "metadata": {
        "command": "exfiltration start",
        "agent_id": "agent_001",
        "target": "*************",
        "activity_type": "exfiltration",
        "raw_entry": {
          "timestamp": "2023-01-15T14:42:00Z",
          "agent_id": "agent_001",
          "command": "exfiltration start",
          "target": "*************",
          "status": "success",
          "data_size": "50MB"
        },
        "status": "success",
        "data_size": "50MB"
      }
    }
  ],
  "comparison_analysis": {
    "baseline_event_count": 0,
    "attack_event_count": 0,
    "unique_attack_indicators": {
      "commands": [
        "lateral_movement scan",
        "download /etc/passwd",
        "exfiltration start",
        "execute powershell.exe -c get-process",
        "execute whoami",
        "upload backdoor.exe",
        "beacon checkin",
        "persistence install"
      ]
    },
    "common_indicators": {},
    "suspicious_patterns": [
      {
        "type": "suspicious_commands",
        "description": "Multiple suspicious commands detected: 2/3",
        "timestamp": "2023-01-15T14:15:00+00:00",
        "commands": [
          "beacon checkin",
          "execute whoami",
          "execute powershell.exe -c get-process"
        ],
        "severity": "high"
      },
      {
        "type": "suspicious_commands",
        "description": "Multiple suspicious commands detected: 2/2",
        "timestamp": "2023-01-15T14:40:00+00:00",
        "commands": [
          "beacon checkin",
          "exfiltration start"
        ],
        "severity": "high"
      }
    ],
    "timeline_anomalies": [],
    "severity_changes": {
      "baseline": {},
      "attack": {
        "low": 2,
        "high": 5,
        "medium": 2
      },
      "changes": {
        "high": 5,
        "low": 2,
        "medium": 2
      }
    },
    "new_event_types": [
      "c2_command"
    ],
    "comparison_summary": {
      "total_baseline_events": 0,
      "total_attack_events": 9,
      "event_increase_ratio": 9.0,
      "unique_attack_indicators_count": 8,
      "suspicious_patterns_count": 2,
      "timeline_anomalies_count": 0,
      "new_event_types_count": 1,
      "risk_score": 29.0,
      "risk_level": "LOW"
    }
  },
  "recommendations": [
    {
      "category": "Immediate Action",
      "priority": "HIGH",
      "recommendation": "Investigate 6 high-severity security events immediately",
      "details": "High-severity events may indicate active threats or successful attacks"
    },
    {
      "category": "Monitoring",
      "priority": "LOW",
      "recommendation": "Enhance logging and monitoring capabilities",
      "details": "Improve visibility into system and network activities"
    },
    {
      "category": "Documentation",
      "priority": "LOW",
      "recommendation": "Document findings and update incident response procedures",
      "details": "Ensure lessons learned are captured for future incidents"
    }
  ],
  "technical_details": {
    "top_correlations": [],
    "event_samples": [
      {
        "timestamp": "2023-01-15T14:15:45+00:00",
        "source": "c2_log",
        "event_type": "c2_command",
        "category": "attack",
        "severity": "low",
        "description": "C2 beacon: beacon checkin (Agent: agent_001) (Target: *************)",
        "metadata": {
          "command": "beacon checkin",
          "agent_id": "agent_001",
          "target": "*************",
          "activity_type": "beacon",
          "raw_entry": {
            "timestamp": "2023-01-15T14:15:45Z",
            "agent_id": "agent_001",
            "command": "beacon checkin",
            "target": "*************",
            "status": "success",
            "response_size": 256
          },
          "status": "success",
          "response_size": 256
        }
      },
      {
        "timestamp": "2023-01-15T14:15:45+00:00",
        "source": "c2_log",
        "event_type": "c2_command",
        "category": "attack",
        "severity": "medium",
        "description": "C2 reconnaissance: reconnaissance (Agent: agent_001) (Target: *************)",
        "metadata": {
          "command": "reconnaissance",
          "agent_id": "agent_001",
          "target": "*************",
          "activity_type": "reconnaissance",
          "raw_row": {
            "timestamp": "2023-01-15 14:15:45",
            "agent_id": "agent_001",
            "command": "reconnaissance",
            "target": "*************",
            "status": "success",
            "details": "network scan"
          }
        }
      },
      {
        "timestamp": "2023-01-15T14:16:00+00:00",
        "source": "c2_log",
        "event_type": "c2_command",
        "category": "attack",
        "severity": "high",
        "description": "C2 command: execute whoami (Agent: agent_001) (Target: *************)",
        "metadata": {
          "command": "execute whoami",
          "agent_id": "agent_001",
          "target": "*************",
          "activity_type": "command",
          "raw_entry": {
            "timestamp": "2023-01-15T14:16:00Z",
            "agent_id": "agent_001",
            "command": "execute whoami",
            "target": "*************",
            "status": "success",
            "response": "admin"
          },
          "status": "success",
          "response": "admin"
        }
      },
      {
        "timestamp": "2023-01-15T14:17:30+00:00",
        "source": "c2_log",
        "event_type": "c2_command",
        "category": "attack",
        "severity": "high",
        "description": "C2 command: execute powershell.exe -c Get-Process (Agent: agent_001) (Target: *************)",
        "metadata": {
          "command": "execute powershell.exe -c Get-Process",
          "agent_id": "agent_001",
          "target": "*************",
          "activity_type": "command",
          "raw_entry": {
            "timestamp": "2023-01-15T14:17:30Z",
            "agent_id": "agent_001",
            "command": "execute powershell.exe -c Get-Process",
            "target": "*************",
            "status": "success",
            "response_size": 2048
          },
          "status": "success",
          "response_size": 2048
        }
      },
      {
        "timestamp": "2023-01-15T14:18:00+00:00",
        "source": "c2_log",
        "event_type": "c2_command",
        "category": "attack",
        "severity": "low",
        "description": "C2 unknown: privilege_escalation (Agent: agent_001) (Target: *************)",
        "metadata": {
          "command": "privilege_escalation",
          "agent_id": "agent_001",
          "target": "*************",
          "activity_type": "unknown",
          "raw_row": {
            "timestamp": "2023-01-15 14:18:00",
            "agent_id": "agent_001",
            "command": "privilege_escalation",
            "target": "*************",
            "status": "success",
            "details": "UAC bypass"
          }
        }
      },
      {
        "timestamp": "2023-01-15T14:20:00+00:00",
        "source": "c2_log",
        "event_type": "c2_command",
        "category": "attack",
        "severity": "medium",
        "description": "C2 download: download /etc/passwd (Agent: agent_001) (Target: *************)",
        "metadata": {
          "command": "download /etc/passwd",
          "agent_id": "agent_001",
          "target": "*************",
          "activity_type": "download",
          "raw_entry": {
            "timestamp": "2023-01-15T14:20:00Z",
            "agent_id": "agent_001",
            "command": "download /etc/passwd",
            "target": "*************",
            "status": "success",
            "file_size": 1024
          },
          "status": "success",
          "file_size": 1024
        }
      },
      {
        "timestamp": "2023-01-15T14:22:00+00:00",
        "source": "c2_log",
        "event_type": "c2_command",
        "category": "attack",
        "severity": "low",
        "description": "C2 unknown: credential_dump (Agent: agent_001) (Target: *************)",
        "metadata": {
          "command": "credential_dump",
          "agent_id": "agent_001",
          "target": "*************",
          "activity_type": "unknown",
          "raw_row": {
            "timestamp": "2023-01-15 14:22:00",
            "agent_id": "agent_001",
            "command": "credential_dump",
            "target": "*************",
            "status": "success",
            "details": "mimikatz"
          }
        }
      },
      {
        "timestamp": "2023-01-15T14:25:00+00:00",
        "source": "c2_log",
        "event_type": "c2_command",
        "category": "attack",
        "severity": "medium",
        "description": "C2 upload: upload backdoor.exe (Agent: agent_001) (Target: *************)",
        "metadata": {
          "command": "upload backdoor.exe",
          "agent_id": "agent_001",
          "target": "*************",
          "activity_type": "upload",
          "raw_entry": {
            "timestamp": "2023-01-15T14:25:00Z",
            "agent_id": "agent_001",
            "command": "upload backdoor.exe",
            "target": "*************",
            "status": "success",
            "file_size": 51200
          },
          "status": "success",
          "file_size": 51200
        }
      },
      {
        "timestamp": "2023-01-15T14:28:00+00:00",
        "source": "c2_log",
        "event_type": "c2_command",
        "category": "attack",
        "severity": "low",
        "description": "C2 unknown: keylogger_start (Agent: agent_001) (Target: *************)",
        "metadata": {
          "command": "keylogger_start",
          "agent_id": "agent_001",
          "target": "*************",
          "activity_type": "unknown",
          "raw_row": {
            "timestamp": "2023-01-15 14:28:00",
            "agent_id": "agent_001",
            "command": "keylogger_start",
            "target": "*************",
            "status": "success",
            "details": "capture keystrokes"
          }
        }
      },
      {
        "timestamp": "2023-01-15T14:30:00+00:00",
        "source": "c2_log",
        "event_type": "c2_command",
        "category": "attack",
        "severity": "high",
        "description": "C2 persistence: persistence install (Agent: agent_001) (Target: *************)",
        "metadata": {
          "command": "persistence install",
          "agent_id": "agent_001",
          "target": "*************",
          "activity_type": "persistence",
          "raw_entry": {
            "timestamp": "2023-01-15T14:30:00Z",
            "agent_id": "agent_001",
            "command": "persistence install",
            "target": "*************",
            "status": "success",
            "method": "registry"
          },
          "status": "success",
          "method": "registry"
        }
      }
    ]
  }
}</pre>
    </div>
</body>
</html>