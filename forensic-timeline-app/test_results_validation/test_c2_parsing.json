{"input_path": "/home/<USER>/TFTPCAP/forensic-timeline-app/test_data/attack/c2_server.json", "category": "attack", "file_type": "auto", "total_events": 9, "events": [{"event_id": "17096228-2676-4cd4-abde-7436eedc1b42", "timestamp": "2023-01-15T14:15:45+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 beacon: beacon checkin (Agent: agent_001) (Target: *************)", "severity": "low", "confidence": null, "metadata": {"command": "beacon checkin", "agent_id": "agent_001", "target": "*************", "activity_type": "beacon", "raw_entry": {"timestamp": "2023-01-15T14:15:45Z", "agent_id": "agent_001", "command": "beacon checkin", "target": "*************", "status": "success", "response_size": 256}, "status": "success", "response_size": 256}, "correlation_id": null, "related_events": []}, {"event_id": "78a46317-1d32-4180-adc7-04148d325f93", "timestamp": "2023-01-15T14:16:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: execute whoami (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "execute whoami", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_entry": {"timestamp": "2023-01-15T14:16:00Z", "agent_id": "agent_001", "command": "execute whoami", "target": "*************", "status": "success", "response": "admin"}, "status": "success", "response": "admin"}, "correlation_id": null, "related_events": []}, {"event_id": "b06431d4-2bdb-4c2e-ab1c-53c327df806d", "timestamp": "2023-01-15T14:17:30+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: execute powershell.exe -c Get-Process (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "execute powershell.exe -c Get-Process", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_entry": {"timestamp": "2023-01-15T14:17:30Z", "agent_id": "agent_001", "command": "execute powershell.exe -c Get-Process", "target": "*************", "status": "success", "response_size": 2048}, "status": "success", "response_size": 2048}, "correlation_id": null, "related_events": []}, {"event_id": "001db3ba-81f8-4dd2-901f-4ad9762cf93f", "timestamp": "2023-01-15T14:20:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 download: download /etc/passwd (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "download /etc/passwd", "agent_id": "agent_001", "target": "*************", "activity_type": "download", "raw_entry": {"timestamp": "2023-01-15T14:20:00Z", "agent_id": "agent_001", "command": "download /etc/passwd", "target": "*************", "status": "success", "file_size": 1024}, "status": "success", "file_size": 1024}, "correlation_id": null, "related_events": []}, {"event_id": "6b907cc7-097b-46f7-8c52-a15f75d1df03", "timestamp": "2023-01-15T14:25:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 upload: upload backdoor.exe (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "upload backdoor.exe", "agent_id": "agent_001", "target": "*************", "activity_type": "upload", "raw_entry": {"timestamp": "2023-01-15T14:25:00Z", "agent_id": "agent_001", "command": "upload backdoor.exe", "target": "*************", "status": "success", "file_size": 51200}, "status": "success", "file_size": 51200}, "correlation_id": null, "related_events": []}, {"event_id": "9e928458-0f45-4d74-a56d-ca2e563b7718", "timestamp": "2023-01-15T14:30:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 persistence: persistence install (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "persistence install", "agent_id": "agent_001", "target": "*************", "activity_type": "persistence", "raw_entry": {"timestamp": "2023-01-15T14:30:00Z", "agent_id": "agent_001", "command": "persistence install", "target": "*************", "status": "success", "method": "registry"}, "status": "success", "method": "registry"}, "correlation_id": null, "related_events": []}, {"event_id": "7cfa4827-9d1b-4364-a68e-5289b8953e00", "timestamp": "2023-01-15T14:35:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 lateral_movement: lateral_movement scan (Agent: agent_001) (Target: ***********/24)", "severity": "high", "confidence": null, "metadata": {"command": "lateral_movement scan", "agent_id": "agent_001", "target": "***********/24", "activity_type": "lateral_movement", "raw_entry": {"timestamp": "2023-01-15T14:35:00Z", "agent_id": "agent_001", "command": "lateral_movement scan", "target": "***********/24", "status": "success", "hosts_found": 15}, "status": "success", "hosts_found": 15}, "correlation_id": null, "related_events": []}, {"event_id": "39d7ccc0-3f69-4c2a-8489-e2edf3845d80", "timestamp": "2023-01-15T14:40:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 beacon: beacon checkin (Agent: agent_002) (Target: ************)", "severity": "low", "confidence": null, "metadata": {"command": "beacon checkin", "agent_id": "agent_002", "target": "************", "activity_type": "beacon", "raw_entry": {"timestamp": "2023-01-15T14:40:00Z", "agent_id": "agent_002", "command": "beacon checkin", "target": "************", "status": "success", "response_size": 256}, "status": "success", "response_size": 256}, "correlation_id": null, "related_events": []}, {"event_id": "096d605d-830f-4170-80e7-5bd874a08697", "timestamp": "2023-01-15T14:42:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 exfiltration: exfiltration start (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "exfiltration start", "agent_id": "agent_001", "target": "*************", "activity_type": "exfiltration", "raw_entry": {"timestamp": "2023-01-15T14:42:00Z", "agent_id": "agent_001", "command": "exfiltration start", "target": "*************", "status": "success", "data_size": "50MB"}, "status": "success", "data_size": "50MB"}, "correlation_id": null, "related_events": []}]}