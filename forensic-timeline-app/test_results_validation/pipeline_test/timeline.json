{"session_id": "838ef38c-e4f1-4541-9523-03379ded7d02", "created_at": "2025-07-02T01:59:38.791484", "total_events": 9, "events": [{"event_id": "33310b02-24bc-4a08-9c4a-a90e10f3b139", "timestamp": "2023-01-15T14:15:45+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 beacon: beacon checkin (Agent: agent_001) (Target: *************)", "severity": "low", "confidence": null, "metadata": {"command": "beacon checkin", "agent_id": "agent_001", "target": "*************", "activity_type": "beacon", "raw_entry": {"timestamp": "2023-01-15T14:15:45Z", "agent_id": "agent_001", "command": "beacon checkin", "target": "*************", "status": "success", "response_size": 256}, "status": "success", "response_size": 256}, "correlation_id": null, "related_events": []}, {"event_id": "be6e9f8d-dbc9-4584-a9dc-1f7a5299924d", "timestamp": "2023-01-15T14:16:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: execute whoami (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "execute whoami", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_entry": {"timestamp": "2023-01-15T14:16:00Z", "agent_id": "agent_001", "command": "execute whoami", "target": "*************", "status": "success", "response": "admin"}, "status": "success", "response": "admin"}, "correlation_id": null, "related_events": []}, {"event_id": "322c14fe-609a-47cd-abdf-f9b3e50f74ef", "timestamp": "2023-01-15T14:17:30+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: execute powershell.exe -c Get-Process (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "execute powershell.exe -c Get-Process", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_entry": {"timestamp": "2023-01-15T14:17:30Z", "agent_id": "agent_001", "command": "execute powershell.exe -c Get-Process", "target": "*************", "status": "success", "response_size": 2048}, "status": "success", "response_size": 2048}, "correlation_id": null, "related_events": []}, {"event_id": "811d70e0-8a93-405d-88c3-57ed13a5144c", "timestamp": "2023-01-15T14:20:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 download: download /etc/passwd (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "download /etc/passwd", "agent_id": "agent_001", "target": "*************", "activity_type": "download", "raw_entry": {"timestamp": "2023-01-15T14:20:00Z", "agent_id": "agent_001", "command": "download /etc/passwd", "target": "*************", "status": "success", "file_size": 1024}, "status": "success", "file_size": 1024}, "correlation_id": null, "related_events": []}, {"event_id": "ef0cf2bd-cc59-4c05-af30-3a3df7c37d80", "timestamp": "2023-01-15T14:25:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 upload: upload backdoor.exe (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "upload backdoor.exe", "agent_id": "agent_001", "target": "*************", "activity_type": "upload", "raw_entry": {"timestamp": "2023-01-15T14:25:00Z", "agent_id": "agent_001", "command": "upload backdoor.exe", "target": "*************", "status": "success", "file_size": 51200}, "status": "success", "file_size": 51200}, "correlation_id": null, "related_events": []}, {"event_id": "7501c925-8ea9-4c8c-89c4-c446568cc570", "timestamp": "2023-01-15T14:30:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 persistence: persistence install (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "persistence install", "agent_id": "agent_001", "target": "*************", "activity_type": "persistence", "raw_entry": {"timestamp": "2023-01-15T14:30:00Z", "agent_id": "agent_001", "command": "persistence install", "target": "*************", "status": "success", "method": "registry"}, "status": "success", "method": "registry"}, "correlation_id": null, "related_events": []}, {"event_id": "4de3fcec-f02f-4175-b404-df1964eaaf3d", "timestamp": "2023-01-15T14:35:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 lateral_movement: lateral_movement scan (Agent: agent_001) (Target: ***********/24)", "severity": "high", "confidence": null, "metadata": {"command": "lateral_movement scan", "agent_id": "agent_001", "target": "***********/24", "activity_type": "lateral_movement", "raw_entry": {"timestamp": "2023-01-15T14:35:00Z", "agent_id": "agent_001", "command": "lateral_movement scan", "target": "***********/24", "status": "success", "hosts_found": 15}, "status": "success", "hosts_found": 15}, "correlation_id": null, "related_events": []}, {"event_id": "0645577d-a256-4f08-8789-0f578912c0e1", "timestamp": "2023-01-15T14:40:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 beacon: beacon checkin (Agent: agent_002) (Target: ************)", "severity": "low", "confidence": null, "metadata": {"command": "beacon checkin", "agent_id": "agent_002", "target": "************", "activity_type": "beacon", "raw_entry": {"timestamp": "2023-01-15T14:40:00Z", "agent_id": "agent_002", "command": "beacon checkin", "target": "************", "status": "success", "response_size": 256}, "status": "success", "response_size": 256}, "correlation_id": null, "related_events": []}, {"event_id": "7058f1a6-29fd-475e-b916-e4d741326d07", "timestamp": "2023-01-15T14:42:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 exfiltration: exfiltration start (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "exfiltration start", "agent_id": "agent_001", "target": "*************", "activity_type": "exfiltration", "raw_entry": {"timestamp": "2023-01-15T14:42:00Z", "agent_id": "agent_001", "command": "exfiltration start", "target": "*************", "status": "success", "data_size": "50MB"}, "status": "success", "data_size": "50MB"}, "correlation_id": null, "related_events": []}]}