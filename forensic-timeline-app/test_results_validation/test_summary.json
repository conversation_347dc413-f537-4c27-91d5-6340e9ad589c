{"summary": {"passed": 11, "failed": 0, "success_rate": 100.0}, "detailed_results": [{"test": "Web Server Health Check", "passed": true, "message": "Status: 200"}, {"test": "Main Page Load", "passed": true, "message": "Status: 200"}, {"test": "CLI Version Command", "passed": true, "message": "Return code: 0"}, {"test": "CLI Help Command", "passed": true, "message": "Return code: 0"}, {"test": "C2 Log Parsing", "passed": true, "message": "Parsed 9 events"}, {"test": "Event Correlation", "passed": true, "message": "Found 36 correlations"}, {"test": "Baseline Comparison", "passed": true, "message": "Risk score: 29.0"}, {"test": "HTML Report Generation", "passed": true, "message": "Report size: 18296 chars"}, {"test": "Complete Analysis Pipeline", "passed": true, "message": "Generated 9 events"}, {"test": "Sessions API Endpoint", "passed": true, "message": "Status: 200"}, {"test": "Upload Page Endpoint", "passed": true, "message": "Status: 200"}]}