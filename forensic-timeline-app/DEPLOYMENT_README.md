# 🔍 Forensic Timeline Analysis - Deployment Guide

A comprehensive, containerized forensic analysis platform that correlates network traffic captures and system logs across baseline and attack scenarios to produce unified timelines with interactive visualization and professional reporting.

## 🌟 **Key Features**

### 📊 **Core Analysis Capabilities**
- ✅ **Multi-source Data Processing**: PCAP files, Windows EVTX, Linux syslogs, C2 logs
- ✅ **Network Traffic Analysis**: Integrated Suricata IDS and Zeek network analyzer
- ✅ **Event Correlation Engine**: Advanced correlation with 5 intelligent rules
- ✅ **Baseline vs Attack Comparison**: Automated threat assessment and risk scoring
- ✅ **Interactive Timeline Visualization**: Web-based timeline with vis.js
- ✅ **Professional Report Generation**: HTML, JSON, and Markdown reports

### 🚀 **Advanced Features**
- ✅ **Risk Assessment**: Automated risk scoring (0-100) with threat level classification
- ✅ **Suspicious Pattern Detection**: ML-based anomaly detection
- ✅ **Baseline Filtering**: Intelligent noise reduction to highlight threats
- ✅ **Real-time Processing**: Live progress tracking and status updates
- ✅ **Multi-format Export**: Comprehensive data export capabilities

## 📋 **System Requirements**

### **Minimum Requirements**
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 10GB free space
- **OS**: Linux (Ubuntu 20.04+), macOS, Windows with WSL2

### **Recommended for Production**
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **Network**: Isolated analysis environment

## 🐳 **Quick Start with Docker (Recommended)**

### **Step 1: Install Prerequisites**
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add user to docker group (logout/login required)
sudo usermod -aG docker $USER
```

### **Step 2: Deploy Application**
```bash
# Extract deployment package
tar -xzf forensic-timeline-deployment-*.tar.gz
cd forensic-timeline-deployment-*

# One-command deployment
./deploy.sh deploy

# Application will be available at:
# Web Interface: http://localhost:5000
# API Health: http://localhost:5000/api/health
```

### **Step 3: Verify Installation**
```bash
# Check application status
./deploy.sh status

# View logs
./deploy.sh logs

# Test API health
curl http://localhost:5000/api/health
```

## 🎯 **Usage Guide**

### **Web Interface**
1. **Access**: Open http://localhost:5000 in your browser
2. **Upload Files**: Drag and drop PCAP/log files
3. **Categorize**: Mark files as "Baseline" or "Attack"
4. **Analyze**: Start analysis and monitor progress
5. **Explore**: View interactive timeline and correlations
6. **Report**: Generate comprehensive reports

### **Command Line Interface**
```bash
# Complete analysis pipeline
docker exec -it forensic-timeline-analysis python cli.py analyze \
  --baseline-logs baseline.log \
  --attack-logs attack.log \
  --output /app/results/analysis \
  --correlate

# Parse logs only
docker exec -it forensic-timeline-analysis python cli.py parse-logs \
  --input /app/uploads/logfile.json \
  --category attack \
  --output /app/results/events.json

# Generate comparison report
docker exec -it forensic-timeline-analysis python cli.py compare \
  --baseline-events baseline.json \
  --attack-events attack.json \
  --output /app/results/comparison.json

# Create comprehensive report
docker exec -it forensic-timeline-analysis python cli.py report \
  --timeline timeline.json \
  --correlations correlations.json \
  --comparison comparison.json \
  --output /app/results/report.html \
  --format html
```

## 🔧 **Management Commands**

### **Application Control**
```bash
./deploy.sh start     # Start containers
./deploy.sh stop      # Stop containers
./deploy.sh restart   # Restart containers
./deploy.sh logs      # View real-time logs
./deploy.sh status    # Check application status
```

### **Maintenance**
```bash
./deploy.sh package   # Create deployment package
./deploy.sh clean     # Remove containers and images
./deploy.sh build     # Rebuild Docker image
```

### **Data Management**
```bash
# Access container shell
docker exec -it forensic-timeline-analysis /bin/bash

# Copy files to container
docker cp local_file.pcap forensic-timeline-analysis:/app/uploads/

# Copy results from container
docker cp forensic-timeline-analysis:/app/results/ ./local_results/

# View persistent volumes
docker volume ls | grep forensic
```

## 📁 **File Structure**

```
forensic-timeline-app/
├── app/                    # Application source code
├── docker/                 # Docker configuration
├── test_data/             # Sample test data
├── docker-compose.yml     # Container orchestration
├── deploy.sh             # Deployment script
├── cli.py               # Command-line interface
└── run.py              # Application entry point
```

## 🔒 **Security Considerations**

### **Network Security**
- Run on isolated network segment
- Use firewall rules to restrict access
- Consider VPN access for remote users

### **Data Security**
- Uploaded files are stored in Docker volumes
- Results are persistent across container restarts
- Consider encryption for sensitive data

### **Access Control**
- Default deployment has no authentication
- Consider adding reverse proxy with authentication
- Monitor access logs for suspicious activity

## 🚨 **Troubleshooting**

### **Common Issues**

**Container won't start:**
```bash
# Check Docker daemon
sudo systemctl status docker

# Check logs
./deploy.sh logs

# Rebuild image
./deploy.sh clean && ./deploy.sh deploy
```

**Application not responding:**
```bash
# Check health status
curl http://localhost:5000/api/health

# Restart application
./deploy.sh restart

# Check resource usage
docker stats forensic-timeline-analysis
```

**Out of disk space:**
```bash
# Clean up Docker
docker system prune -a

# Check volume usage
docker system df

# Clean application data
./deploy.sh clean
```

### **Performance Tuning**

**For large PCAP files:**
- Increase container memory limit
- Use SSD storage for better I/O
- Process files in smaller chunks

**For many concurrent users:**
- Scale horizontally with multiple containers
- Use load balancer (nginx)
- Increase resource limits

## 📞 **Support**

### **Logs and Debugging**
```bash
# Application logs
./deploy.sh logs

# Container inspection
docker inspect forensic-timeline-analysis

# Resource monitoring
docker stats forensic-timeline-analysis
```

### **Health Monitoring**
- **Health Check**: http://localhost:5000/api/health
- **Application Status**: `./deploy.sh status`
- **Container Logs**: `./deploy.sh logs`

## 🎉 **Success Indicators**

✅ **Deployment Successful When:**
- Health check returns HTTP 200
- Web interface loads at http://localhost:5000
- File upload works without errors
- Analysis completes and generates results
- Reports are generated successfully

✅ **Ready for Production When:**
- All tests pass (100% success rate)
- Performance meets requirements
- Security measures implemented
- Monitoring and alerting configured
- Backup and recovery procedures tested

---

**🔍 Forensic Timeline Analysis - Professional forensic investigation made simple.**
