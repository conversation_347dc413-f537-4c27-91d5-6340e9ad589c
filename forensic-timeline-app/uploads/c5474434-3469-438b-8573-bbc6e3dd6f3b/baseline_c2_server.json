{"timestamp": "2023-01-15T14:15:45Z", "agent_id": "agent_001", "command": "beacon checkin", "target": "*************", "status": "success", "response_size": 256}
{"timestamp": "2023-01-15T14:16:00Z", "agent_id": "agent_001", "command": "execute whoami", "target": "*************", "status": "success", "response": "admin"}
{"timestamp": "2023-01-15T14:17:30Z", "agent_id": "agent_001", "command": "execute powershell.exe -c Get-Process", "target": "*************", "status": "success", "response_size": 2048}
{"timestamp": "2023-01-15T14:20:00Z", "agent_id": "agent_001", "command": "download /etc/passwd", "target": "*************", "status": "success", "file_size": 1024}
{"timestamp": "2023-01-15T14:25:00Z", "agent_id": "agent_001", "command": "upload backdoor.exe", "target": "*************", "status": "success", "file_size": 51200}
{"timestamp": "2023-01-15T14:30:00Z", "agent_id": "agent_001", "command": "persistence install", "target": "*************", "status": "success", "method": "registry"}
{"timestamp": "2023-01-15T14:35:00Z", "agent_id": "agent_001", "command": "lateral_movement scan", "target": "***********/24", "status": "success", "hosts_found": 15}
{"timestamp": "2023-01-15T14:40:00Z", "agent_id": "agent_002", "command": "beacon checkin", "target": "************", "status": "success", "response_size": 256}
{"timestamp": "2023-01-15T14:42:00Z", "agent_id": "agent_001", "command": "exfiltration start", "target": "*************", "status": "success", "data_size": "50MB"}
