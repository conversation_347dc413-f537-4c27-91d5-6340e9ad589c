{"input_path": "test_data/attack/c2_server.json", "category": "attack", "file_type": "auto", "total_events": 9, "events": [{"event_id": "d4e1f6e7-93fd-4862-ac87-fc8740fed7cd", "timestamp": "2023-01-15T14:15:45+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 beacon: beacon checkin (Agent: agent_001) (Target: *************)", "severity": "low", "confidence": null, "metadata": {"command": "beacon checkin", "agent_id": "agent_001", "target": "*************", "activity_type": "beacon", "raw_entry": {"timestamp": "2023-01-15T14:15:45Z", "agent_id": "agent_001", "command": "beacon checkin", "target": "*************", "status": "success", "response_size": 256}, "status": "success", "response_size": 256}, "correlation_id": null, "related_events": []}, {"event_id": "4076bea8-9de1-4af5-b20f-cb4fea9b7cf1", "timestamp": "2023-01-15T14:16:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: execute whoami (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "execute whoami", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_entry": {"timestamp": "2023-01-15T14:16:00Z", "agent_id": "agent_001", "command": "execute whoami", "target": "*************", "status": "success", "response": "admin"}, "status": "success", "response": "admin"}, "correlation_id": null, "related_events": []}, {"event_id": "cf6a9bdc-26cd-4db8-b3c6-1744bd7de61e", "timestamp": "2023-01-15T14:17:30+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: execute powershell.exe -c Get-Process (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "execute powershell.exe -c Get-Process", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_entry": {"timestamp": "2023-01-15T14:17:30Z", "agent_id": "agent_001", "command": "execute powershell.exe -c Get-Process", "target": "*************", "status": "success", "response_size": 2048}, "status": "success", "response_size": 2048}, "correlation_id": null, "related_events": []}, {"event_id": "14a06946-cf8a-489d-9090-412fe021bcfd", "timestamp": "2023-01-15T14:20:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 download: download /etc/passwd (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "download /etc/passwd", "agent_id": "agent_001", "target": "*************", "activity_type": "download", "raw_entry": {"timestamp": "2023-01-15T14:20:00Z", "agent_id": "agent_001", "command": "download /etc/passwd", "target": "*************", "status": "success", "file_size": 1024}, "status": "success", "file_size": 1024}, "correlation_id": null, "related_events": []}, {"event_id": "b9847f69-304b-480d-a23a-303cd690997c", "timestamp": "2023-01-15T14:25:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 upload: upload backdoor.exe (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "upload backdoor.exe", "agent_id": "agent_001", "target": "*************", "activity_type": "upload", "raw_entry": {"timestamp": "2023-01-15T14:25:00Z", "agent_id": "agent_001", "command": "upload backdoor.exe", "target": "*************", "status": "success", "file_size": 51200}, "status": "success", "file_size": 51200}, "correlation_id": null, "related_events": []}, {"event_id": "f48d14e6-d95f-422d-8fe2-37238f0163d1", "timestamp": "2023-01-15T14:30:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 persistence: persistence install (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "persistence install", "agent_id": "agent_001", "target": "*************", "activity_type": "persistence", "raw_entry": {"timestamp": "2023-01-15T14:30:00Z", "agent_id": "agent_001", "command": "persistence install", "target": "*************", "status": "success", "method": "registry"}, "status": "success", "method": "registry"}, "correlation_id": null, "related_events": []}, {"event_id": "238ab953-0f2d-408e-9f09-25bcc7b4fe75", "timestamp": "2023-01-15T14:35:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 lateral_movement: lateral_movement scan (Agent: agent_001) (Target: ***********/24)", "severity": "high", "confidence": null, "metadata": {"command": "lateral_movement scan", "agent_id": "agent_001", "target": "***********/24", "activity_type": "lateral_movement", "raw_entry": {"timestamp": "2023-01-15T14:35:00Z", "agent_id": "agent_001", "command": "lateral_movement scan", "target": "***********/24", "status": "success", "hosts_found": 15}, "status": "success", "hosts_found": 15}, "correlation_id": null, "related_events": []}, {"event_id": "02cd2a69-6609-49c8-9b36-0b2542e7b778", "timestamp": "2023-01-15T14:40:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 beacon: beacon checkin (Agent: agent_002) (Target: ************)", "severity": "low", "confidence": null, "metadata": {"command": "beacon checkin", "agent_id": "agent_002", "target": "************", "activity_type": "beacon", "raw_entry": {"timestamp": "2023-01-15T14:40:00Z", "agent_id": "agent_002", "command": "beacon checkin", "target": "************", "status": "success", "response_size": 256}, "status": "success", "response_size": 256}, "correlation_id": null, "related_events": []}, {"event_id": "bd17b1d4-b2ad-4b24-a264-c4cecd13c8b8", "timestamp": "2023-01-15T14:42:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 exfiltration: exfiltration start (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "exfiltration start", "agent_id": "agent_001", "target": "*************", "activity_type": "exfiltration", "raw_entry": {"timestamp": "2023-01-15T14:42:00Z", "agent_id": "agent_001", "command": "exfiltration start", "target": "*************", "status": "success", "data_size": "50MB"}, "status": "success", "data_size": "50MB"}, "correlation_id": null, "related_events": []}]}