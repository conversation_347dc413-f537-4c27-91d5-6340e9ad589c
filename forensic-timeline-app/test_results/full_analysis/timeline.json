{"session_id": "f7e54cd2-3989-4a21-acf7-422abd21726c", "created_at": "2025-07-01T21:31:14.350075", "total_events": 14, "events": [{"event_id": "bf09af63-3106-4729-8fb8-44789d0a339f", "timestamp": "2023-01-15T14:15:45+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 beacon: beacon checkin (Agent: agent_001) (Target: *************)", "severity": "low", "confidence": null, "metadata": {"command": "beacon checkin", "agent_id": "agent_001", "target": "*************", "activity_type": "beacon", "raw_entry": {"timestamp": "2023-01-15T14:15:45Z", "agent_id": "agent_001", "command": "beacon checkin", "target": "*************", "status": "success", "response_size": 256}, "status": "success", "response_size": 256}, "correlation_id": null, "related_events": []}, {"event_id": "f813a12d-eda3-4027-94e9-1983fb50385a", "timestamp": "2023-01-15T14:15:45+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 reconnaissance: reconnaissance (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "reconnaissance", "agent_id": "agent_001", "target": "*************", "activity_type": "reconnaissance", "raw_row": {"timestamp": "2023-01-15 14:15:45", "agent_id": "agent_001", "command": "reconnaissance", "target": "*************", "status": "success", "details": "network scan"}}, "correlation_id": null, "related_events": []}, {"event_id": "ce880820-1f24-41c2-ae28-4a0db38f11f3", "timestamp": "2023-01-15T14:16:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: execute whoami (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "execute whoami", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_entry": {"timestamp": "2023-01-15T14:16:00Z", "agent_id": "agent_001", "command": "execute whoami", "target": "*************", "status": "success", "response": "admin"}, "status": "success", "response": "admin"}, "correlation_id": null, "related_events": []}, {"event_id": "c123da11-69b3-4adb-b814-4f96e7e11300", "timestamp": "2023-01-15T14:17:30+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: execute powershell.exe -c Get-Process (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "execute powershell.exe -c Get-Process", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_entry": {"timestamp": "2023-01-15T14:17:30Z", "agent_id": "agent_001", "command": "execute powershell.exe -c Get-Process", "target": "*************", "status": "success", "response_size": 2048}, "status": "success", "response_size": 2048}, "correlation_id": null, "related_events": []}, {"event_id": "1ccd0944-ebdf-4f3e-8944-3b486932b067", "timestamp": "2023-01-15T14:18:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 unknown: privilege_escalation (Agent: agent_001) (Target: *************)", "severity": "low", "confidence": null, "metadata": {"command": "privilege_escalation", "agent_id": "agent_001", "target": "*************", "activity_type": "unknown", "raw_row": {"timestamp": "2023-01-15 14:18:00", "agent_id": "agent_001", "command": "privilege_escalation", "target": "*************", "status": "success", "details": "UAC bypass"}}, "correlation_id": null, "related_events": []}, {"event_id": "1ab1ee02-3252-40ba-be1a-7285c2d53f73", "timestamp": "2023-01-15T14:20:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 download: download /etc/passwd (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "download /etc/passwd", "agent_id": "agent_001", "target": "*************", "activity_type": "download", "raw_entry": {"timestamp": "2023-01-15T14:20:00Z", "agent_id": "agent_001", "command": "download /etc/passwd", "target": "*************", "status": "success", "file_size": 1024}, "status": "success", "file_size": 1024}, "correlation_id": null, "related_events": []}, {"event_id": "38b88cf0-246b-46f0-bbef-0a4c9c80416a", "timestamp": "2023-01-15T14:22:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 unknown: credential_dump (Agent: agent_001) (Target: *************)", "severity": "low", "confidence": null, "metadata": {"command": "credential_dump", "agent_id": "agent_001", "target": "*************", "activity_type": "unknown", "raw_row": {"timestamp": "2023-01-15 14:22:00", "agent_id": "agent_001", "command": "credential_dump", "target": "*************", "status": "success", "details": "<PERSON><PERSON><PERSON><PERSON>"}}, "correlation_id": null, "related_events": []}, {"event_id": "717418f4-faa4-4d7e-90af-9f4866ac9d9d", "timestamp": "2023-01-15T14:25:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 upload: upload backdoor.exe (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "upload backdoor.exe", "agent_id": "agent_001", "target": "*************", "activity_type": "upload", "raw_entry": {"timestamp": "2023-01-15T14:25:00Z", "agent_id": "agent_001", "command": "upload backdoor.exe", "target": "*************", "status": "success", "file_size": 51200}, "status": "success", "file_size": 51200}, "correlation_id": null, "related_events": []}, {"event_id": "9953d45e-968c-44a3-98ee-fe36c288fd72", "timestamp": "2023-01-15T14:28:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 unknown: keylogger_start (Agent: agent_001) (Target: *************)", "severity": "low", "confidence": null, "metadata": {"command": "keylogger_start", "agent_id": "agent_001", "target": "*************", "activity_type": "unknown", "raw_row": {"timestamp": "2023-01-15 14:28:00", "agent_id": "agent_001", "command": "keylogger_start", "target": "*************", "status": "success", "details": "capture keystrokes"}}, "correlation_id": null, "related_events": []}, {"event_id": "82f3e5d7-a683-4445-bba7-80b874da514c", "timestamp": "2023-01-15T14:30:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 persistence: persistence install (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "persistence install", "agent_id": "agent_001", "target": "*************", "activity_type": "persistence", "raw_entry": {"timestamp": "2023-01-15T14:30:00Z", "agent_id": "agent_001", "command": "persistence install", "target": "*************", "status": "success", "method": "registry"}, "status": "success", "method": "registry"}, "correlation_id": null, "related_events": []}, {"event_id": "f588a0d8-3171-4c0e-8d27-ffe488bcbecb", "timestamp": "2023-01-15T14:33:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: screenshot (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "screenshot", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_row": {"timestamp": "2023-01-15 14:33:00", "agent_id": "agent_001", "command": "screenshot", "target": "*************", "status": "success", "details": "desktop capture"}}, "correlation_id": null, "related_events": []}, {"event_id": "40479d10-87b1-4c67-ad99-696833615224", "timestamp": "2023-01-15T14:35:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 lateral_movement: lateral_movement scan (Agent: agent_001) (Target: ***********/24)", "severity": "high", "confidence": null, "metadata": {"command": "lateral_movement scan", "agent_id": "agent_001", "target": "***********/24", "activity_type": "lateral_movement", "raw_entry": {"timestamp": "2023-01-15T14:35:00Z", "agent_id": "agent_001", "command": "lateral_movement scan", "target": "***********/24", "status": "success", "hosts_found": 15}, "status": "success", "hosts_found": 15}, "correlation_id": null, "related_events": []}, {"event_id": "44bf808d-42c0-4bb6-b74d-6653454efb27", "timestamp": "2023-01-15T14:40:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 beacon: beacon checkin (Agent: agent_002) (Target: ************)", "severity": "low", "confidence": null, "metadata": {"command": "beacon checkin", "agent_id": "agent_002", "target": "************", "activity_type": "beacon", "raw_entry": {"timestamp": "2023-01-15T14:40:00Z", "agent_id": "agent_002", "command": "beacon checkin", "target": "************", "status": "success", "response_size": 256}, "status": "success", "response_size": 256}, "correlation_id": null, "related_events": []}, {"event_id": "9a668b02-7acb-47b6-bcae-f2afa00b01b1", "timestamp": "2023-01-15T14:42:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 exfiltration: exfiltration start (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "exfiltration start", "agent_id": "agent_001", "target": "*************", "activity_type": "exfiltration", "raw_entry": {"timestamp": "2023-01-15T14:42:00Z", "agent_id": "agent_001", "command": "exfiltration start", "target": "*************", "status": "success", "data_size": "50MB"}, "status": "success", "data_size": "50MB"}, "correlation_id": null, "related_events": []}]}