{"session_id": "166abe6e-b826-4659-9fcf-34ef4112739c", "created_at": "2025-07-02T01:33:24.448902", "total_events": 14, "events": [{"event_id": "74ce4152-2b9c-4c0b-85ea-9cdb2976def0", "timestamp": "2023-01-15T14:15:45+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 beacon: beacon checkin (Agent: agent_001) (Target: *************)", "severity": "low", "confidence": null, "metadata": {"command": "beacon checkin", "agent_id": "agent_001", "target": "*************", "activity_type": "beacon", "raw_entry": {"timestamp": "2023-01-15T14:15:45Z", "agent_id": "agent_001", "command": "beacon checkin", "target": "*************", "status": "success", "response_size": 256}, "status": "success", "response_size": 256}, "correlation_id": null, "related_events": []}, {"event_id": "ca33d627-c847-439b-9aae-8a9ab81eb824", "timestamp": "2023-01-15T14:15:45+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 reconnaissance: reconnaissance (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "reconnaissance", "agent_id": "agent_001", "target": "*************", "activity_type": "reconnaissance", "raw_row": {"timestamp": "2023-01-15 14:15:45", "agent_id": "agent_001", "command": "reconnaissance", "target": "*************", "status": "success", "details": "network scan"}}, "correlation_id": null, "related_events": []}, {"event_id": "c8e5f445-d0a7-46fa-b686-f95ce866d18f", "timestamp": "2023-01-15T14:16:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: execute whoami (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "execute whoami", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_entry": {"timestamp": "2023-01-15T14:16:00Z", "agent_id": "agent_001", "command": "execute whoami", "target": "*************", "status": "success", "response": "admin"}, "status": "success", "response": "admin"}, "correlation_id": null, "related_events": []}, {"event_id": "3c9f6b7b-0dcd-4619-827a-16820807bea9", "timestamp": "2023-01-15T14:17:30+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: execute powershell.exe -c Get-Process (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "execute powershell.exe -c Get-Process", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_entry": {"timestamp": "2023-01-15T14:17:30Z", "agent_id": "agent_001", "command": "execute powershell.exe -c Get-Process", "target": "*************", "status": "success", "response_size": 2048}, "status": "success", "response_size": 2048}, "correlation_id": null, "related_events": []}, {"event_id": "34f294b8-8ddd-4b6e-8618-888825d269c2", "timestamp": "2023-01-15T14:18:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 unknown: privilege_escalation (Agent: agent_001) (Target: *************)", "severity": "low", "confidence": null, "metadata": {"command": "privilege_escalation", "agent_id": "agent_001", "target": "*************", "activity_type": "unknown", "raw_row": {"timestamp": "2023-01-15 14:18:00", "agent_id": "agent_001", "command": "privilege_escalation", "target": "*************", "status": "success", "details": "UAC bypass"}}, "correlation_id": null, "related_events": []}, {"event_id": "567c3575-be42-405e-be2b-a092a3f70295", "timestamp": "2023-01-15T14:20:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 download: download /etc/passwd (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "download /etc/passwd", "agent_id": "agent_001", "target": "*************", "activity_type": "download", "raw_entry": {"timestamp": "2023-01-15T14:20:00Z", "agent_id": "agent_001", "command": "download /etc/passwd", "target": "*************", "status": "success", "file_size": 1024}, "status": "success", "file_size": 1024}, "correlation_id": null, "related_events": []}, {"event_id": "7cf2075c-66b9-4225-bf42-fa0ac66a39cc", "timestamp": "2023-01-15T14:22:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 unknown: credential_dump (Agent: agent_001) (Target: *************)", "severity": "low", "confidence": null, "metadata": {"command": "credential_dump", "agent_id": "agent_001", "target": "*************", "activity_type": "unknown", "raw_row": {"timestamp": "2023-01-15 14:22:00", "agent_id": "agent_001", "command": "credential_dump", "target": "*************", "status": "success", "details": "<PERSON><PERSON><PERSON><PERSON>"}}, "correlation_id": null, "related_events": []}, {"event_id": "0ebd3259-d594-42b8-af78-105905dcb773", "timestamp": "2023-01-15T14:25:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 upload: upload backdoor.exe (Agent: agent_001) (Target: *************)", "severity": "medium", "confidence": null, "metadata": {"command": "upload backdoor.exe", "agent_id": "agent_001", "target": "*************", "activity_type": "upload", "raw_entry": {"timestamp": "2023-01-15T14:25:00Z", "agent_id": "agent_001", "command": "upload backdoor.exe", "target": "*************", "status": "success", "file_size": 51200}, "status": "success", "file_size": 51200}, "correlation_id": null, "related_events": []}, {"event_id": "fc19a3cf-965c-41f5-9fbf-889a0047da1b", "timestamp": "2023-01-15T14:28:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 unknown: keylogger_start (Agent: agent_001) (Target: *************)", "severity": "low", "confidence": null, "metadata": {"command": "keylogger_start", "agent_id": "agent_001", "target": "*************", "activity_type": "unknown", "raw_row": {"timestamp": "2023-01-15 14:28:00", "agent_id": "agent_001", "command": "keylogger_start", "target": "*************", "status": "success", "details": "capture keystrokes"}}, "correlation_id": null, "related_events": []}, {"event_id": "225ed945-dbd9-4f04-979a-4c148e00c622", "timestamp": "2023-01-15T14:30:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 persistence: persistence install (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "persistence install", "agent_id": "agent_001", "target": "*************", "activity_type": "persistence", "raw_entry": {"timestamp": "2023-01-15T14:30:00Z", "agent_id": "agent_001", "command": "persistence install", "target": "*************", "status": "success", "method": "registry"}, "status": "success", "method": "registry"}, "correlation_id": null, "related_events": []}, {"event_id": "43c862c3-0081-4377-b699-d2b1fb8107fb", "timestamp": "2023-01-15T14:33:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 command: screenshot (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "screenshot", "agent_id": "agent_001", "target": "*************", "activity_type": "command", "raw_row": {"timestamp": "2023-01-15 14:33:00", "agent_id": "agent_001", "command": "screenshot", "target": "*************", "status": "success", "details": "desktop capture"}}, "correlation_id": null, "related_events": []}, {"event_id": "f0a91e14-1812-4fc5-a70f-3758918ce14d", "timestamp": "2023-01-15T14:35:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 lateral_movement: lateral_movement scan (Agent: agent_001) (Target: ***********/24)", "severity": "high", "confidence": null, "metadata": {"command": "lateral_movement scan", "agent_id": "agent_001", "target": "***********/24", "activity_type": "lateral_movement", "raw_entry": {"timestamp": "2023-01-15T14:35:00Z", "agent_id": "agent_001", "command": "lateral_movement scan", "target": "***********/24", "status": "success", "hosts_found": 15}, "status": "success", "hosts_found": 15}, "correlation_id": null, "related_events": []}, {"event_id": "179331ac-0714-4caa-b719-047f230408ac", "timestamp": "2023-01-15T14:40:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 beacon: beacon checkin (Agent: agent_002) (Target: ************)", "severity": "low", "confidence": null, "metadata": {"command": "beacon checkin", "agent_id": "agent_002", "target": "************", "activity_type": "beacon", "raw_entry": {"timestamp": "2023-01-15T14:40:00Z", "agent_id": "agent_002", "command": "beacon checkin", "target": "************", "status": "success", "response_size": 256}, "status": "success", "response_size": 256}, "correlation_id": null, "related_events": []}, {"event_id": "3c6f5323-db18-452e-b445-0db3a38e9c98", "timestamp": "2023-01-15T14:42:00+00:00", "source": "c2_log", "event_type": "c2_command", "category": "attack", "description": "C2 exfiltration: exfiltration start (Agent: agent_001) (Target: *************)", "severity": "high", "confidence": null, "metadata": {"command": "exfiltration start", "agent_id": "agent_001", "target": "*************", "activity_type": "exfiltration", "raw_entry": {"timestamp": "2023-01-15T14:42:00Z", "agent_id": "agent_001", "command": "exfiltration start", "target": "*************", "status": "success", "data_size": "50MB"}, "status": "success", "data_size": "50MB"}, "correlation_id": null, "related_events": []}]}