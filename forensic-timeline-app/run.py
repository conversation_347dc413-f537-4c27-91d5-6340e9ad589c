#!/usr/bin/env python3
"""
Main entry point for the Forensic Timeline Analysis application.
"""

import os
from app import create_app
from app.config import config

def main():
    """Main application entry point."""
    # Get configuration from environment
    config_name = os.environ.get('FLASK_ENV', 'development')

    # Create Flask application
    app = create_app(config[config_name])

    # Initialize configuration
    config[config_name].init_app(app)

    # Run the application
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = config_name == 'development'

    app.run(host=host, port=port, debug=debug)

if __name__ == '__main__':
    main()