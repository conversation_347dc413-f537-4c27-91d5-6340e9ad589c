#!/usr/bin/env python3
"""
Test script for log parsers in the forensic timeline analysis application.
"""

import sys
import os
import tempfile
from datetime import datetime

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_syslog_parser():
    """Test the syslog parser with sample data."""
    print("🔍 Testing Syslog Parser...")

    try:
        from app.parsers.syslog_parser import SyslogParser
        from app.models.event import EventCategory

        # Create sample syslog data
        sample_syslog = """Jan 15 14:30:25 server1 sshd[1234]: Accepted password for admin from ************* port 22 ssh2
Jan 15 14:31:10 server1 sshd[1235]: Failed password for invalid user hacker from ******** port 22 ssh2
Jan 15 14:32:05 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls
Jan 15 14:33:15 server1 kernel: segfault at 0000000000000000 ip 00007f8b8c0a1234 sp 00007fff12345678 error 4
Jan 15 14:34:20 server1 systemd[1]: Started Apache HTTP Server
"""

        # Write to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
            f.write(sample_syslog)
            temp_file = f.name

        try:
            parser = SyslogParser()
            events = parser.parse_syslog_file(temp_file, EventCategory.ATTACK)

            print(f"✅ Parsed {len(events)} syslog events")

            for event in events:
                print(f"  - {event.timestamp}: {event.description} ({event.severity})")

            return True

        finally:
            os.unlink(temp_file)

    except Exception as e:
        print(f"❌ Syslog parser test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_c2_parser():
    """Test the C2 log parser with sample data."""
    print("\n🔍 Testing C2 Log Parser...")

    try:
        from app.parsers.c2_log_parser import C2LogParser
        from app.models.event import EventCategory

        # Create sample C2 JSON data
        sample_c2_json = """{"timestamp": "2023-01-15T14:30:25Z", "agent_id": "agent001", "command": "execute powershell.exe", "target": "************"}
{"timestamp": "2023-01-15T14:31:10Z", "agent_id": "agent001", "command": "download sensitive.txt", "target": "************"}
{"timestamp": "2023-01-15T14:32:05Z", "agent_id": "agent002", "command": "beacon checkin", "target": "********"}
"""

        # Write to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write(sample_c2_json)
            temp_file = f.name

        try:
            parser = C2LogParser()
            events = parser.parse_c2_log_file(temp_file, EventCategory.ATTACK, 'json')

            print(f"✅ Parsed {len(events)} C2 events")

            for event in events:
                print(f"  - {event.timestamp}: {event.description} ({event.severity})")

            return True

        finally:
            os.unlink(temp_file)

    except Exception as e:
        print(f"❌ C2 parser test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_c2_csv_parser():
    """Test the C2 parser with CSV data."""
    print("\n🔍 Testing C2 CSV Parser...")

    try:
        from app.parsers.c2_log_parser import C2LogParser
        from app.models.event import EventCategory

        # Create sample C2 CSV data
        sample_c2_csv = """timestamp,agent_id,command,target,status
2023-01-15 14:30:25,agent001,execute cmd.exe,************,success
2023-01-15 14:31:10,agent001,upload malware.exe,************,success
2023-01-15 14:32:05,agent002,reconnaissance scan,********,success
"""

        # Write to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_c2_csv)
            temp_file = f.name

        try:
            parser = C2LogParser()
            events = parser.parse_c2_log_file(temp_file, EventCategory.ATTACK, 'csv')

            print(f"✅ Parsed {len(events)} C2 CSV events")

            for event in events:
                print(f"  - {event.timestamp}: {event.description} ({event.severity})")

            return True

        finally:
            os.unlink(temp_file)

    except Exception as e:
        print(f"❌ C2 CSV parser test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_normalizer():
    """Test the log normalizer with multiple file types."""
    print("\n🔍 Testing Log Normalizer...")

    try:
        from app.parsers.log_normalizer import LogNormalizer
        from app.models.event import EventCategory

        # Create sample files
        sample_syslog = """Jan 15 14:30:25 server1 sshd[1234]: Accepted password for admin from ************* port 22 ssh2
Jan 15 14:31:10 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/cat /etc/passwd
"""

        sample_c2 = """{"timestamp": "2023-01-15T14:32:25Z", "agent_id": "agent001", "command": "lateral movement", "target": "*************"}
"""

        temp_files = []

        try:
            # Create syslog file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
                f.write(sample_syslog)
                syslog_file = f.name
                temp_files.append(syslog_file)

            # Create C2 file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                f.write(sample_c2)
                c2_file = f.name
                temp_files.append(c2_file)

            # Test normalizer
            normalizer = LogNormalizer()

            log_files = [
                {'filepath': syslog_file, 'file_type': 'auto'},
                {'filepath': c2_file, 'file_type': 'auto'}
            ]

            events = normalizer.parse_log_files(log_files, EventCategory.ATTACK)

            print(f"✅ Normalized {len(events)} events from multiple sources")

            for event in events:
                print(f"  - {event.source.value}: {event.description}")

            return True

        finally:
            for temp_file in temp_files:
                try:
                    os.unlink(temp_file)
                except:
                    pass

    except Exception as e:
        print(f"❌ Log normalizer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all parser tests."""
    print("🚀 Starting Log Parser Tests")
    print("=" * 50)

    tests = [
        test_syslog_parser,
        test_c2_parser,
        test_c2_csv_parser,
        test_log_normalizer
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1

    print("\n" + "=" * 50)
    print(f"📊 Parser Test Results: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 All parser tests passed! Log parsing implementation is working.")
        return 0
    else:
        print("⚠️  Some parser tests failed. Please check the implementation.")
        return 1

if __name__ == '__main__':
    sys.exit(main())