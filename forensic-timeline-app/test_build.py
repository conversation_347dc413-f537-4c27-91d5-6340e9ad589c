#!/usr/bin/env python3
"""
Test script to validate the forensic timeline analysis application build.
"""

import sys
import os
import traceback
from datetime import datetime

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_imports():
    """Test that all modules can be imported successfully."""
    print("🔍 Testing module imports...")

    try:
        # Test core Flask app
        from app import create_app
        from app.config import Config, DevelopmentConfig
        print("✅ Flask application imports successful")

        # Test data models
        from app.models.event import Event, EventType, EventSource, EventCategory
        from app.models.timeline import Timeline, AnalysisSession, AnalysisStatus
        from app.models.analysis import AnalysisResult, ComparisonResult, Finding
        print("✅ Data models imports successful")

        # Test analyzers
        from app.analyzers.network_analyzer import NetworkAnalyzer, SuricataAnalyzer, ZeekAnalyzer
        print("✅ Network analyzers imports successful")

        return True

    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_data_models():
    """Test data model creation and serialization."""
    print("\n🔍 Testing data models...")

    try:
        from app.models.event import Event, EventType, EventSource, EventCategory
        from app.models.timeline import Timeline

        # Create a test event
        event = Event(
            timestamp=datetime.now(),
            source=EventSource.SURICATA,
            event_type=EventType.NETWORK_ALERT,
            category=EventCategory.ATTACK,
            description="Test alert",
            severity="high",
            metadata={"src_ip": "*************", "dst_ip": "********"}
        )

        # Test serialization
        event_dict = event.to_dict()
        event_json = event.to_json()

        print(f"✅ Event created with ID: {event.event_id}")
        print(f"✅ Event serialization successful")

        # Create timeline and add event
        timeline = Timeline(session_id="test-session")
        timeline.add_event(event)
        timeline.sort_events()

        print(f"✅ Timeline created with {len(timeline.events)} events")

        return True

    except Exception as e:
        print(f"❌ Data model test failed: {e}")
        traceback.print_exc()
        return False

def test_flask_app():
    """Test Flask application creation."""
    print("\n🔍 Testing Flask application...")

    try:
        from app import create_app
        from app.config import DevelopmentConfig

        # Create test app
        app = create_app(DevelopmentConfig)

        # Test app configuration
        assert app.config['DEBUG'] == True
        assert app.config['TESTING'] == False

        # Test that routes are registered
        with app.test_client() as client:
            # Test main routes
            response = client.get('/')
            print(f"✅ Home route status: {response.status_code}")

            # Test API health check
            response = client.get('/api/health')
            print(f"✅ API health check status: {response.status_code}")

            if response.status_code == 200:
                data = response.get_json()
                print(f"✅ API response: {data.get('status', 'unknown')}")

        print("✅ Flask application test successful")
        return True

    except Exception as e:
        print(f"❌ Flask application test failed: {e}")
        traceback.print_exc()
        return False

def test_configuration():
    """Test configuration settings."""
    print("\n🔍 Testing configuration...")

    try:
        from app.config import Config, DevelopmentConfig, ProductionConfig

        # Test base config
        config = Config()
        assert hasattr(config, 'SECRET_KEY')
        assert hasattr(config, 'UPLOAD_FOLDER')
        assert hasattr(config, 'SURICATA_PATH')
        assert hasattr(config, 'ZEEK_PATH')

        print("✅ Base configuration valid")

        # Test development config
        dev_config = DevelopmentConfig()
        assert dev_config.DEBUG == True
        print("✅ Development configuration valid")

        # Test production config
        prod_config = ProductionConfig()
        assert prod_config.DEBUG == False
        print("✅ Production configuration valid")

        return True

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        traceback.print_exc()
        return False

def test_cli_interface():
    """Test CLI interface."""
    print("\n🔍 Testing CLI interface...")

    try:
        # Import CLI module
        import cli

        # Test that main function exists
        assert hasattr(cli, 'main')
        assert hasattr(cli, 'analyze_command')
        assert hasattr(cli, 'test_tools_command')
        assert hasattr(cli, 'version_command')

        print("✅ CLI interface structure valid")
        return True

    except Exception as e:
        print(f"❌ CLI interface test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Forensic Timeline Analysis Build Tests")
    print("=" * 60)

    tests = [
        test_imports,
        test_configuration,
        test_data_models,
        test_flask_app,
        test_cli_interface
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1

    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 All tests passed! Build is ready for Docker containerization.")
        return 0
    else:
        print("⚠️  Some tests failed. Please fix issues before proceeding.")
        return 1

if __name__ == '__main__':
    sys.exit(main())