#!/bin/bash

# Forensic Timeline Analysis - Deployment Script
# This script builds and deploys the containerized application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="forensic-timeline-analysis"
IMAGE_NAME="forensic-timeline"
CONTAINER_NAME="forensic-timeline-analysis"
PORT="5000"

# Functions
print_header() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "  Forensic Timeline Analysis Deployment"
    echo "=============================================="
    echo -e "${NC}"
}

print_step() {
    echo -e "${YELLOW}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    print_step "Checking dependencies..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Dependencies check passed"
}

build_image() {
    print_step "Building Docker image..."
    
    docker build -f docker/Dockerfile -t $IMAGE_NAME:latest .
    
    if [ $? -eq 0 ]; then
        print_success "Docker image built successfully"
    else
        print_error "Failed to build Docker image"
        exit 1
    fi
}

stop_existing() {
    print_step "Stopping existing containers..."
    
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        docker stop $CONTAINER_NAME
        docker rm $CONTAINER_NAME
        print_success "Stopped existing container"
    else
        echo "No existing container found"
    fi
}

deploy_container() {
    print_step "Deploying container..."
    
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        print_success "Container deployed successfully"
    else
        print_error "Failed to deploy container"
        exit 1
    fi
}

wait_for_health() {
    print_step "Waiting for application to be healthy..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:$PORT/api/health &> /dev/null; then
            print_success "Application is healthy and ready"
            return 0
        fi
        
        echo "Attempt $attempt/$max_attempts - waiting for application..."
        sleep 2
        ((attempt++))
    done
    
    print_error "Application failed to become healthy within timeout"
    return 1
}

show_status() {
    print_step "Application status:"
    
    echo ""
    echo "Container Status:"
    docker ps -f name=$CONTAINER_NAME --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    echo "Application URLs:"
    echo "  Web Interface: http://localhost:$PORT"
    echo "  API Health:    http://localhost:$PORT/api/health"
    
    echo ""
    echo "Useful Commands:"
    echo "  View logs:     docker-compose logs -f"
    echo "  Stop app:      docker-compose down"
    echo "  Restart app:   docker-compose restart"
    echo "  Shell access:  docker exec -it $CONTAINER_NAME /bin/bash"
}

create_backup() {
    print_step "Creating deployment package..."
    
    backup_dir="forensic-timeline-deployment-$(date +%Y%m%d_%H%M%S)"
    mkdir -p $backup_dir
    
    # Copy essential files
    cp -r app $backup_dir/
    cp -r docker $backup_dir/
    cp -r test_data $backup_dir/
    cp docker-compose.yml $backup_dir/
    cp deploy.sh $backup_dir/
    cp cli.py $backup_dir/
    cp run.py $backup_dir/
    cp README.md $backup_dir/
    
    # Create archive
    tar -czf "${backup_dir}.tar.gz" $backup_dir
    rm -rf $backup_dir
    
    print_success "Deployment package created: ${backup_dir}.tar.gz"
}

# Main execution
main() {
    print_header
    
    case "${1:-deploy}" in
        "build")
            check_dependencies
            build_image
            ;;
        "deploy")
            check_dependencies
            build_image
            stop_existing
            deploy_container
            wait_for_health
            show_status
            ;;
        "start")
            docker-compose up -d
            wait_for_health
            show_status
            ;;
        "stop")
            print_step "Stopping application..."
            docker-compose down
            print_success "Application stopped"
            ;;
        "restart")
            print_step "Restarting application..."
            docker-compose restart
            wait_for_health
            show_status
            ;;
        "logs")
            docker-compose logs -f
            ;;
        "status")
            show_status
            ;;
        "package")
            create_backup
            ;;
        "clean")
            print_step "Cleaning up..."
            docker-compose down -v
            docker rmi $IMAGE_NAME:latest 2>/dev/null || true
            print_success "Cleanup complete"
            ;;
        *)
            echo "Usage: $0 {build|deploy|start|stop|restart|logs|status|package|clean}"
            echo ""
            echo "Commands:"
            echo "  build    - Build Docker image only"
            echo "  deploy   - Full deployment (build + deploy + health check)"
            echo "  start    - Start existing containers"
            echo "  stop     - Stop containers"
            echo "  restart  - Restart containers"
            echo "  logs     - Show application logs"
            echo "  status   - Show application status"
            echo "  package  - Create deployment package"
            echo "  clean    - Remove containers and images"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
