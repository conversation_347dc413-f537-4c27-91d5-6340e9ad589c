{\rtf1\ansi\ansicpg1252\cocoartf2822
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fnil\fcharset0 .SFNS-Semibold;\f1\fnil\fcharset0 .SFNS-Regular;\f2\fnil\fcharset0 HelveticaNeue-Bold;
\f3\fnil\fcharset0 .AppleSystemUIFontMonospaced-Regular;\f4\fnil\fcharset0 .SFNS-RegularItalic;}
{\colortbl;\red255\green255\blue255;\red14\green14\blue14;}
{\*\expandedcolortbl;;\cssrgb\c6700\c6700\c6700;}
\margl1440\margr1440\vieww29740\viewh18040\viewkind0
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f0\b\fs44 \cf2 Detailed Specifications for Forensic Timeline Analysis App
\f1\b0\fs28 \
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f0\b\fs34 \cf2 Overview
\f1\b0\fs28 \
\
This application will be a 
\f2\b containerized forensic analysis tool
\f1\b0  that correlates network traffic captures and system logs across baseline and attack scenarios. The goal is to streamline offline analysis of PCAP files and execution logs, producing a combined timeline of events. All necessary dependencies (network analyzers, log parsers, etc.) will be pre-installed in the container for a turnkey user experience. The user can upload a \'93baseline\'94 PCAP & logs (normal operation) and an \'93exploitation\'94 PCAP & logs (during an attack) and have the app automatically analyze and compare them. The output includes an interactive timeline of events, detected anomalies (e.g. IDS alerts), and a comprehensive report for the investigation.\
\

\f0\b\fs34 Application Interfaces and Usage Modes
\f1\b0\fs28 \
\
The app will support multiple interfaces to accommodate different use cases:\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	
\f2\b Web Interface:
\f1\b0  A user-friendly web application for interactive analysis. Users can drag-and-drop or select files for upload, then view results (timeline, alerts, reports) in their browser. The web UI will guide the user through uploading baseline vs attack files and then display the correlated analysis results.\
	\'95	
\f2\b Command-Line Interface (CLI):
\f1\b0  A CLI tool (e.g. 
\f3 forensic_analyze.py
\f1 ) for advanced users or automation. The CLI will accept file path arguments for PCAPs and logs (including flags to designate baseline vs attack files) and then execute the same analysis workflow. Results can be output as a structured report (HTML/JSON) or printed summary.\
	\'95	
\f2\b REST API:
\f1\b0  A programmatic interface to integrate with other tools or AI agents. For example, an endpoint (e.g. 
\f3 POST /analyze
\f1 ) will accept file uploads (multipart form data or a JSON with file references) and analysis parameters. The API will run the analysis and return results in JSON (or provide a download link for the report). This enables automation, such as an Augment AI agent calling the API with captured data to get correlated findings.\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f2\b \cf2 Single-User Local Analysis:
\f1\b0  The application is designed for a single analyst on a local machine (no multi-user web service). There is no user account system \'96 one instance serves one user\'92s analysis session. This simplifies deployment and avoids concurrency issues. (Multiple analyses can be run one after another or via separate instances if needed.) Data is processed and stored locally within the container, and no external network connectivity is required for operation (ideal for sensitive forensic data).\
\

\f0\b\fs34 File Upload and Data Ingestion
\f1\b0\fs28 \
\

\f2\b Upload Mechanism:
\f1\b0  Users will be able to easily provide input files to the app:\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	In the 
\f2\b web UI
\f1\b0 , provide a drag-and-drop area and a file selection dialog. The user can upload at least four files: a baseline PCAP, an attack PCAP, baseline logs, and attack logs. These can be uploaded individually or as a batch. The interface will clearly label which file is which (e.g. fields for \'93Baseline Network Capture\'94, \'93Attack Network Capture\'94, \'93Baseline Logs\'94, \'93Attack Logs\'94).\
	\'95	The 
\f2\b CLI
\f1\b0  will accept file paths or directories via arguments (e.g. 
\f3 --baseline-pcap
\f1 , 
\f3 --attack-pcap
\f1 , 
\f3 --baseline-logs
\f1 , 
\f3 --attack-logs
\f1 ). If multiple log files are involved (for example, several log files from a system), the CLI can accept a directory or multiple 
\f3 --log
\f1  options.\
	\'95	The 
\f2\b API
\f1\b0  will allow sending files as part of a request. For example, a JSON structure with file content (encoded) or an HTTP multipart upload. The API should accept the same logical inputs: baseline PCAP, attack PCAP, and corresponding logs.\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f2\b \cf2 Supported File Types:
\f1\b0 \
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	
\f2\b PCAP Files:
\f1\b0  Standard packet capture files (
\f3 .pcap
\f1  or 
\f3 .pcapng
\f1 ) are the primary input for network traffic. The app will handle potentially large PCAPs by processing them with efficient tools inside the container (discussed below).\
	\'95	
\f2\b Execution Logs / System Logs:
\f1\b0  The app will ingest text-based log files or structured logs from both the attacker\'92s platform and the victim machine. This may include:\
\pard\tqr\tx500\tx660\li660\fi-660\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	
\f2\b Command/Control (C2) Logs:
\f1\b0  e.g. a timeline of commands executed by the attacker\'92s C2 framework or manual attack steps (with timestamps and descriptions of actions). These might be in CSV, JSON, or plain text with timestamps. The app will provide parsing logic or configurable format definitions for these (e.g. regex patterns to extract timestamp, command, result).\
	\'95	
\f2\b Victim System Logs:
\f1\b0  e.g. Windows Event Logs, Linux syslogs, application logs from the target machine captured during the baseline and attack. The app will support common log formats: if Windows 
\f2\b .evtx
\f1\b0  event log files are provided, the container will include a parser to convert these to a readable format (for example, using an EVTX-to-JSON converter or Log2Timeline). In practice, tools like Timesketch/Plaso demonstrate how 
\f3 .evtx
\f1  files can be parsed into timeline events for analysis . In our app, an embedded parser (or a call to a utility like 
\f3 evtx_dump
\f1  or 
\f3 plaso
\f1 ) will extract timestamped events from Windows logs. For text-based logs (e.g. 
\f3 .log
\f1  files), the user may specify the log type (syslog, Apache log, etc.) or the app may auto-detect common formats and parse accordingly. Each log line will be interpreted into a structured event (with time, source, message, etc.).\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	
\f2\b Multiple Files:
\f1\b0  The app should allow multiple log files per analysis (since an attack may involve several log sources). All provided logs for baseline will be aggregated as \'93baseline timeline events\'94, and same for attack. If needed, the UI could allow uploading a ZIP of logs which the app then extracts, but initially a simple multiple-select upload is sufficient.\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f2\b \cf2 File Handling and Storage:
\f1\b0  Uploaded files will be stored in a temporary directory within the container. The app will clearly handle large files by streaming or chunking if necessary (especially for PCAPs). Once uploaded:\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	The server side will verify the file type/format (e.g., ensure the PCAP is valid, ensure logs are text/known format) and then trigger the analysis pipeline. If a file is not in a supported format, the app will return a clear error message (e.g. \'93Unrecognized file format\'94 or \'93Log parsing failed \'96 please provide logs in plain text or EVTX\'94).\
	\'95	No data will leave the container; processing is entirely local (important for forensic integrity). If the user closes the app, files and results can persist in the container\'92s storage for that session, but since this is single-user, it\'92s acceptable to reset state on each run unless an option is provided to save the analysis session.\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f0\b\fs34 \cf2 Network Traffic Analysis (Offline Suricata & Zeek Integration)
\f1\b0\fs28 \
\
To analyze PCAP files offline, the container will include network security monitoring tools 
\f2\b Suricata
\f1\b0  and 
\f2\b Zeek
\f1\b0  (formerly Bro). These tools will generate rich insights from the packet captures:\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	
\f2\b Suricata IDS Analysis:
\f1\b0  Suricata will be used in 
\f2\b offline mode
\f1\b0  to process the PCAPs with a ruleset and produce alert logs. Upon receiving a PCAP (baseline or attack), the app will execute Suricata on it (e.g. 
\f3 suricata -r <file.pcap> -l <output_dir>
\f1 ). Suricata will inspect the traffic and output events in its JSON log (eve.json), including any intrusion alerts triggered by known malicious patterns, as well as protocol metadata if configured. This approach is similar to known workflows where a web service accepts a PCAP upload, runs Suricata, and returns the resulting log . We will bundle a recent Suricata version and a set of detection rules (e.g. the Emerging Threats open ruleset) in the container. The user may have the option to update or customize rules, but by default it will detect common exploits present in the attack PCAP. Suricata\'92s output (especially the 
\f3 eve.json
\f1  log) will be parsed by our app to extract events such as: timestamps of alerts, alert descriptions (signature names), source/destination IPs, domains, file hashes, etc. Each of these will become timeline events (with an \'93Alert\'94 or \'93Network Indicator\'94 label). If needed, separate categories for different Suricata event types (alert, DNS query, HTTP request, etc.) can be distinguished.\
	\'95	
\f2\b Zeek Network Analysis:
\f1\b0  Zeek will also be integrated for a more complete offline analysis of the PCAPs. Zeek can read a PCAP and produce comprehensive logs of network activities (connections, HTTP sessions, DNS queries, TLS handshakes, etc.) . It provides a high-level holistic view of captured traffic, which is extremely useful for timeline creation. When the user uploads a PCAP, the app will run Zeek (e.g. 
\f3 zeek -r <file.pcap>
\f1 ) on it. Zeek will generate various log files (conn.log, dns.log, http.log, files.log, etc.) in a designated folder. Our app will particularly focus on logs that indicate notable events: for example, the 
\f2\b notice.log
\f1\b0  (Zeek\'92s notice framework logs potentially suspicious activities or policy violations), the 
\f2\b weird.log
\f1\b0  (protocol anomalies), and any 
\f2\b file or malware logs
\f1\b0  Zeek produces. These entries will be parsed into timeline events. Even normal connection events can be included (especially for baseline vs attack comparison, e.g. new IP addresses contacted during the attack). Using Zeek in tandem with Suricata ensures we catch both signature-based alerts and broader activity context. (PcapMonkey is an example project that uses Suricata + Zeek together for PCAP analysis , validating this approach).\
\
Both Suricata and Zeek operate offline (no live capture needed), aligning with the requirement that \'93everything is offline here.\'94 After processing, the intermediate outputs (Suricata JSON, Zeek logs) will either be stored as part of the analysis results or cleaned up after parsing. The container image will have Suricata and Zeek pre-installed and configured. This may slightly increase image size, but ensures immediate functionality. If needed, the container could be split (e.g. using multiple containers via Docker Compose, as projects like Dalton do ), but a single-container design is acceptable for simplicity.\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f4\i \cf2 Example:
\f1\i0  If the attack PCAP contains C2 traffic, Suricata might raise alerts for known C2 signatures, and Zeek might log the periodic beacon connections and any file downloads. These would all become events for the attack timeline. The baseline PCAP likely has no alerts, and just regular traffic logs (which might be minimal or none if baseline is idle traffic). The comparison will highlight that the attack PCAP generated alerts and unusual connections not seen in baseline.\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f0\b\fs34 \cf2 Log Ingestion and Parsing
\f1\b0\fs28 \
\
Parallel to network analysis, the app will process the 
\f2\b execution and system logs
\f1\b0  provided:\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	
\f2\b Attacker Command Logs:
\f1\b0  These logs (from the C2 platform or manual attack record) detail what actions were executed and when (e.g. exploitation commands, malware deployment, C2 communications). The app will parse these logs line by line. We assume each entry has a timestamp. The parser will recognize the timestamp (supporting various formats \'96 this could be configured or auto-detected, e.g. ISO 8601, or a common format like \'93YYYY-MM-DD HH:MM:SS\'94). It will then extract the action description. For example, a line might read: 
\f3 2025-06-30 14:05:23 - Executed exploit payload on target via HTTP
\f1 . This would become a timeline event at 14:05:23 with description \'93Attacker executed exploit payload on target (via HTTP)\'94. If the log includes additional fields (like source IP, target IP, command output), those can be included in the event details. The app might support popular C2 log formats (for instance, Cobalt Strike logs, Metasploit console logs, etc.), or at least provide a simple interface for custom parsing rules (for v1, built-in simple parsing should suffice).\
	\'95	
\f2\b Victim Machine Logs:
\f1\b0  These include operating system and application logs from the target system. They are critical for seeing how the system recorded the attack (e.g. security alerts, new processes, errors). The app will support:\
\pard\tqr\tx500\tx660\li660\fi-660\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	
\f2\b Windows Event Logs (.evtx):
\f1\b0  As noted, the container will have a way to parse EVTX. One approach is to convert them to JSON or CSV using an existing tool or library. For instance, the app could incorporate a utility like 
\f3 evtxdump
\f1  or utilize the Python 
\f3 Evtx
\f1  library to iterate through events. The events will be mapped to the timeline. Key fields like event ID, source (e.g. \'93Security\'94 log, \'93System\'94 log), event description will be included. Tools like Timesketch show that EVTX files can be indexed for timeline analysis , and our app will perform a similar conversion internally (though on a smaller scale, not requiring a full Elasticsearch index unless we choose to integrate one). Each Windows event becomes a timeline entry (timestamped by the event time). For example, a Windows Security log entry for a new login or a Sysmon log for process creation can indicate the attacker\'92s actions from the host perspective.\
	\'95	
\f2\b Linux/macOS Logs:
\f1\b0  If the target was Linux, the app can parse syslog files (/var/log/auth.log, /var/log/syslog, etc.) or any custom application logs provided. The approach is similar: read line by line, parse timestamp and message. Known log formats (like syslog\'92s \'93Month Day HH:MM:SS Host process: message\'94) can be recognized with regex.\
	\'95	
\f2\b Application/Custom Logs:
\f1\b0  The user might have specific logs (e.g. an AV log or a custom app log). The app can attempt to parse any text file with timestamps. If unrecognized, it might default to treating each newline as an event and attempt to parse a datetime. (In future, we could allow the user to provide a parsing template).\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f2\b \cf2 Log Normalization:
\f1\b0  All logs, once parsed, will be normalized to a common schema for correlation. At minimum, each event will have:\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	a timestamp (in a unified format, likely UTC internally),\
	\'95	a source/category (attacker-log, victim-system-log, etc.),\
	\'95	and a message or description.\
We may also keep additional fields like severity or event ID (if available), hostnames, process IDs, etc., to enrich the timeline event details.\
\
For example, if a Windows event log entry indicates a new process (say, malware.exe executed) at 14:05:25, and the attacker log at 14:05:23 says \'93executed payload\'94, and the network log at 14:05:30 shows an outbound connection, the timeline will reflect all of these. This 
\f2\b multi-source ingestion
\f1\b0  sets the stage for correlation.\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f0\b\fs34 \cf2 Event Correlation and Timeline Construction
\f1\b0\fs28 \
\
Once the PCAP and log data is processed, the core of the application is to 
\f2\b correlate events by time
\f1\b0  and present a unified timeline. Here\'92s how the app will accomplish this:\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	
\f2\b Unified Timeline Data Structure:
\f1\b0  The app will merge all events from all sources (network alerts, network traffic events, attacker actions, system logs) into a single chronological sequence. Each event record will include its timestamp, source (which dataset and possibly which system), and a brief description. For clarity, the events could also be tagged with an \'93origin\'94 attribute like \'93Baseline\'94 vs \'93Attack\'94 scenario. This unified list can be stored internally as a Python data structure (list of dicts) or in a small database (like SQLite) if needed for queries.\
	\'95	
\f2\b Time Synchronization:
\f1\b0  A crucial aspect is aligning timestamps. We assume that the system clock on the victim and the attacker logs and the packet capture timestamps are all roughly in sync (in practice, they should be using NTP or at least we have offsets). The app should allow the user to specify if there is a known time offset between datasets (for instance, if the victim machine\'92s clock was 5 minutes fast, the user might input an offset to correct it). Otherwise, we consider all times in UTC or a single timezone. We will document that requirement for accurate correlation. If needed, the app could attempt to auto-detect a large offset (e.g. if events seem consistently off by hours, prompt the user).\
	\'95	
\f2\b Merging Baseline and Attack Events:
\f1\b0  If a baseline dataset is provided, its events will be merged into the timeline as well, but clearly marked. The purpose is to compare normal vs malicious activity. Many baseline events may be routine system events also present during attack (e.g. normal OS background tasks). The app can help identify which events are 
\f2\b exclusive to the attack scenario
\f1\b0 . For example, it can flag events that appear in the attack timeline that have no counterpart in the baseline timeline. The user may toggle viewing baseline events on or off to avoid clutter. Essentially, baseline events serve as a noise filter \'96 by overlaying them, the investigator can ignore those and focus on the new anomalies.\
	\'95	
\f2\b Correlation Analysis:
\f1\b0  Beyond just sorting by time, the app can perform basic correlation to link related events:\
\pard\tqr\tx500\tx660\li660\fi-660\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	It can highlight if an attacker log action closely precedes a system log event or network alert. E.g., if \'93Attacker runs RDP exploit at 10:00:00\'94 and at 10:00:05 a Windows log shows a service crash or new process, the UI might visually connect or group these as related (perhaps by color-coding or by an annotation).\
	\'95	If Suricata flagged an alert (say, C2 traffic) and the attacker log shows a command for data exfiltration, those could be correlated by time and by perhaps IP address. The app could include logic to cross-reference data within events (e.g., an IP or domain in a Suricata alert that also appears in the C2 log or victim log) and highlight such matches. This kind of cross-source pattern matching can be extended, but initially, time-based proximity is the primary correlation.\
	\'95	The result of correlation might also include generating 
\f2\b derived conclusions
\f1\b0  \'96 for instance, \'93Network alert X at 10:05:30 corresponds to malware execution Y logged on victim at 10:05:25.\'94 Such conclusions might be included in the report narrative.\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	
\f2\b Performance Considerations:
\f1\b0  The timeline correlation will operate in-memory for speed (the volume of events is typically manageable; even a few hundred thousand log lines can be handled). If the datasets are large (e.g., millions of events), we might incorporate an indexing approach or rely on an embedded database. However, given the single-case, single-user scope, we anticipate manageable data sizes. The container\'92s memory should be sufficient for typical use (the user should be advised if extremely large PCAPs or logs might be an issue).\
\
Modern forensic log analysis emphasizes exactly this kind of multi-source timeline correlation to reconstruct incidents . Our application will implement these principles by providing a consolidated view where events from different sources line up in sequence, allowing the analyst to identify cause-and-effect relationships and the progression of the attack. Notably, analyzing patterns across multiple logs can reveal the attack sequence clearly  \'96 for example, an attacker command leads to a system change, which triggers network traffic, etc., all visible together.\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f0\b\fs34 \cf2 Timeline Visualization (Interactive vs. Report)
\f1\b0\fs28 \
\

\f2\b Interactive Timeline Interface:
\f1\b0  We recommend visualizing the correlated timeline within the web interface as an interactive component. This means the web UI will feature a timeline chart or graph that the user can scroll, zoom, and filter. Using a JavaScript timeline library (such as vis.js Timeline or similar), we can present events on a horizontal time axis . Key characteristics of this timeline interface:\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	Each event will appear as an item on the timeline at the appropriate timestamp. The item can be a dot or box; if an event has a duration (not common in logs, but e.g. a network connection from time A to B), it could be a span. For simplicity, most events are point-in-time.\
	\'95	We will group or color-code events by their source/type. For example, attacker actions could be in one color (or one row if using grouping), victim system logs in another, and network/IDS events in another. This grouping helps differentiate event types visually. The library should support 
\f2\b groups
\f1\b0  (vis.js allows grouping items on parallel lanes) \'96 e.g., we could have separate lanes for \'93Attacker Log\'94, \'93Victim Log (Baseline)\'94, \'93Victim Log (Attack)\'94, \'93Network Alerts (Baseline)\'94, \'93Network Alerts (Attack)\'94. That way, baseline vs attack and log vs network distinctions can be seen at a glance, while time alignment is maintained across the horizontal axis.\
	\'95	The timeline will be interactive: users can zoom in to a specific minute or second, or zoom out to see the entire timeline spanning hours or days. They can drag the view to pan through time . This is crucial for analyzing sequences of events and spotting clusters of activity.\
	\'95	Clicking on an event will show its details (e.g., full log message, Suricata alert signature, involved IP addresses). A details pane or pop-up can display all available metadata for that event. For example, clicking a Suricata alert might show \'93Alert: Nemucod Downloader Trojan Request [Priority: 1]\\nSrc IP: *************, Dst IP: ************, TCP 80\'94 (information extracted from eve.json) . Clicking a system log event might show the event ID and description from Windows logs, etc.\
	\'95	Filters: The interface will allow filtering events by source or keyword. The user might toggle checkboxes to show/hide certain categories (e.g., uncheck \'93Baseline events\'94 to only see attack events, or filter to only \'93Alerts\'94 vs all network events). They could also search for keywords (like a specific IP address or filename) to highlight matching events. This makes investigation more efficient.\
\
By integrating this timeline into the web app, the analyst can dynamically explore the data. This is generally more powerful than a static report because the user can investigate patterns, adjust the view, and drill down as needed. The 
\f2\b recommendation is strongly in favor of this interactive timeline within the app
\f1\b0  as it provides immediate insight and flexibility. Modern web libraries make this feasible (for instance, vis.js timeline allows interactive dragging, zooming, and even editing of items in the browser ).\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f2\b \cf2 HTML Report Output:
\f1\b0  In addition to the live UI, the app will support generating a 
\f2\b static report
\f1\b0  (in HTML, and possibly PDF) summarizing the findings. This report can include:\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	A textual narrative of the incident timeline (listing key events in order with timestamps and descriptions).\
	\'95	Tables of notable events, for example a table of all Suricata alerts (with time, description, severity), a table of key system log events, etc.\
	\'95	Possibly a screenshot or rendering of the timeline graphic for documentation purposes (though an interactive timeline can\'92t be fully captured in a PDF, we might include a simplified chart or at least an ordered list of events).\
	\'95	Highlighted correlations, e.g., a section \'93Correlation Summary\'94 that describes how certain events align (e.g., \'93At 14:05:23 attacker launched exploit, at 14:05:25 system log recorded a new process, at 14:05:30 network IDS alert triggered for C2 traffic\'94).\
\
The HTML report could be generated automatically after analysis, providing the user something they can save and refer to or share with others. It could even contain embedded references to external knowledge (if this is not air-gapped) \'96 for example, if an IDS alert corresponds to a known CVE, the report might include a link to more info.\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f2\b \cf2 Recommendation on Visualization:
\f1\b0  Use the 
\f2\b interactive timeline in the main interface
\f1\b0  as the primary means of correlation analysis, because it allows the investigator to actively explore the data and find relationships. This interactive approach aligns with best practices in digital forensics tools that emphasize visual timelines and drill-down capability, rather than static charts. However, also provide the ability to 
\f2\b export a report
\f1\b0  for archival and reporting purposes. The HTML/PDF report serves as a deliverable, while the interactive UI serves as the analytical workspace. This combined approach covers both needs. (If resources allow, the app could even host a lightweight web server to present the interactive timeline remotely, but since it\'92s single-user local, this is mainly for the user on their machine.)\
\

\f0\b\fs34 Baseline vs Attack Comparison Functionality
\f1\b0\fs28 \
\
A key feature of this app is the side-by-side analysis of baseline versus exploitation data. The design will explicitly handle comparisons to highlight the impact of the attack:\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	
\f2\b Differentiating Datasets:
\f1\b0  The user will indicate which PCAP/log set is \'93baseline\'94 and which is \'93attack\'94 when uploading (the UI will have separate fields, and the CLI/API have separate parameters). Internally, the app tags events originating from each dataset accordingly. This allows for visual differentiation (different colors or timeline lanes, as discussed) and logical comparison.\
	\'95	
\f2\b Baseline Suppression of Common Events:
\f1\b0  The app can optionally 
\f2\b suppress or downplay baseline events
\f1\b0  in the combined timeline to reduce noise. For instance, if an event (same type and description) appears in both baseline and attack at similar frequencies, it might not be significant (e.g., a daily scheduled task log). The user could enable a filter like \'93show only new/delta events\'94 which would hide events that also occurred during baseline. Conversely, showing all events can help verify that baseline was indeed clean.\
	\'95	
\f2\b Highlighting Novel Network Indicators:
\f1\b0  By comparing the baseline PCAP analysis to the attack PCAP, the app can identify new network indicators that appear only during the attack. For example, if the baseline network traffic shows connections to update servers and internal services, and the attack traffic shows an outbound connection to an unfamiliar IP or domain, the app will flag that domain/IP as suspicious (since it\'92s not in baseline). This could be done by extracting all unique IPs/domains from baseline vs attack and doing a set difference. The report might list \'93New external IPs contacted during attack\'94 as findings. Similarly, any Suricata alerts in attack vs none in baseline are inherently critical differences.\
\pard\tqr\tx500\tx660\li660\fi-660\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	The app will likely generate a 
\f2\b \'93Differences Summary\'94
\f1\b0 : e.g., \'9310 new DNS queries in attack not seen in baseline (domains: evil.com, malware.cn)\'94, \'935 new external IP connections in attack\'94, \'93Suricata triggered 3 alerts in attack (none in baseline)\'94, etc. This gives a quick overview of what changed when the attack happened.\
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	
\f2\b Timeline Overlay:
\f1\b0  The interactive timeline, if grouped by baseline/attack, effectively acts as an overlay. For example, you might see a row for baseline events and one for attack events. If baseline was relatively quiet, the attack row will show bursts of activity where the baseline row is empty. In cases where both have events (like routine system events at the same time each day), you\'92ll see parallel markers, indicating those might be normal. This visual overlay helps distinguish normal vs abnormal chronologically.\
	\'95	
\f2\b User Recommendation and Guidance:
\f1\b0  The UI can provide guidance such as: \'93Upload baseline captures from a period with no attack, and the corresponding logs, then upload the captures/logs from the attack period. The tool will automatically contrast the two.\'94 The output could explicitly note if certain expected baseline events are missing or if baseline wasn\'92t provided (if the user doesn\'92t have baseline, the tool still works, just treats attack data as the only timeline).\
\
In summary, the comparison feature ensures the analyst can quickly zero in on malicious activity by using the baseline as a reference model of normal behavior. This concept is akin to anomaly detection by baseline comparison, and it will be an integral part of both the timeline visualization and the report (highlighting anomalies introduced by the exploitation).\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\sl324\slmult1\pardirnatural\partightenfactor0

\f0\b\fs34 \cf2 Additional Features and Considerations
\f1\b0\fs28 \
\

\f2\b Suricata/Zeek Tuning:
\f1\b0  The app might offer some advanced options, such as the ability to provide a custom Suricata ruleset (via file upload in UI or path in CLI) in case the user has specific signatures for their environment. By default, it ships with a standard ruleset that is updated regularly. Similarly, for Zeek, the app might allow custom Zeek scripts if the user wants to detect something specific (though this is an advanced use; out-of-the-box Zeek will be configured with default policies).\
\

\f2\b Offline Mode and Updates:
\f1\b0  The entire analysis is offline. The container does not require Internet access to function (all rules and parsers are local). If rule updates are needed, the user would update the container or feed in new rules manually. This ensures the tool can be used in lab or classified environments with no connectivity.\
\

\f2\b Container Technology and Deployment:
\f1\b0  We will use Docker to containerize the app. The Docker image will be based on a Linux distribution (e.g. Ubuntu or Alpine) that can support Suricata and Zeek installations. It will include: Python 3 (for the app logic and web server), Suricata IDS, Zeek, and any libraries for log parsing (for EVTX, etc.). The image will expose a port (for the web UI, e.g. port 5000 if using Flask) so that the user can access the UI via browser (e.g. http://localhost:5000). Running the container is as simple as: 
\f3 docker run -p 5000:5000 -v /path/to/data:/data forensic_timeline_app
\f1 . The volume mount is optional (for persistence), but not strictly required since the user can upload files through the UI. We ensure all dependencies are pre-installed so the user doesn\'92t need to configure anything \'96 just launch and use. This approach is in line with known solutions like Dalton which use Docker for quick deployment of a PCAP analysis system .\
\
If the architecture is split (e.g. using docker-compose), one container might run the web application and orchestrate analysis, while other containers run Suricata, Zeek, or an Elastic stack. However, for a single-user tool, we can keep it simple within one container process: the Python backend can call Suricata/Zeek executables directly (since they\'92re installed in the same environment). The trade-off is resource usage, but given typical usage (one analysis at a time), this is acceptable.\
\

\f2\b Performance and Scaling:
\f1\b0  The app is optimized for single-case analysis, so real-time performance is not critical, but responsiveness is important for user experience. When a user uploads a PCAP, the app will show a progress indicator (for example, \'93Analyzing network traffic\'85\'94) because running Suricata or Zeek on a large PCAP can take time. We might implement the analysis in a background thread or task queue so that the web UI doesn\'92t freeze. For instance, upon file upload, create a job, then periodically update the UI with progress (Suricata can output stats logs we could monitor, or simply show a spinner until done). Since it\'92s local, even a heavy PCAP should be processed as fast as the user\'92s CPU allows. Memory usage should be monitored (Zeek and Suricata can both use memory for large captures). The container can be given enough memory via Docker settings. We will document recommended system requirements (e.g. X GB of RAM for Y size of PCAP).\
\

\f2\b Security Considerations:
\f1\b0  All analysis happens within an isolated container, which adds a layer of security. Handling potentially malicious PCAPs or logs is safe in this context, as Suricata and Zeek are designed to process untrusted network data without executing it. We will keep those tools updated to avoid any known parsing vulnerabilities. The web interface will be local and not exposed to the internet by default (unless user deliberately does so), preventing external attackers from accessing the sensitive data or interface. We might not implement authentication (since single-user local), but if needed, a simple access token or local auth could be added for safety.\
\

\f2\b Output Data Management:
\f1\b0  After analysis, the user may download the report from the UI. The raw results (like Suricata\'92s eve.json, Zeek logs, parsed timeline JSON) could be packaged in a results archive if the user wants them. For example, a \'93Download raw results\'94 button could provide a ZIP containing all generated logs and the combined timeline in CSV/JSON form. This would let the user do additional analysis in external tools if desired. It also serves as an evidence package. If the user doesn\'92t download, these files remain in the container; if the container is removed, they go away (unless a volume is used for persistence).\
\

\f2\b Testing with Sample Data:
\f1\b0  We will verify the app using known baseline/attack data. For instance, use a clean PCAP and an attack PCAP from a public dataset, plus synthetic logs, to ensure the timeline correctly shows the malicious events. We\'92ll also test uploading only an attack PCAP & logs without baseline (the app should handle missing baseline gracefully, just displaying the timeline of attack events by itself).\
\

\f2\b Documentation and Usage Guidance:
\f1\b0  The final deliverable to the user will include instructions: how to run the container, how to upload files, and examples of output. The interface itself will label components clearly (e.g. fields named \'93Upload Baseline PCAP\'94). In the report, we\'92ll clarify the scenario (maybe with an introduction section that the user can edit to describe the case).\
\
By covering the above aspects, this specification ensures a comprehensive containerized application that meets the user\'92s needs: a one-stop tool to compare normal vs malicious activities by correlating packet captures with system logs, presented in an easy-to-use interface with powerful timeline visualization.\
\

\f0\b\fs34 References and Supporting Sources
\f1\b0\fs28 \
\pard\tqr\tx100\tx260\li260\fi-260\sl324\slmult1\sb240\partightenfactor0
\cf2 	\'95	Integrating 
\f2\b Suricata
\f1\b0  for offline PCAP analysis is straightforward: one can create a web service that accepts PCAP uploads, runs Suricata on the backend, and returns the results . Our app follows this model, automating Suricata analysis and presenting the 
\f3 eve.json
\f1  alerts to the user.\
	\'95	The solution will be delivered as a 
\f2\b Docker container
\f1\b0 . This approach is inspired by prior work like Dalton (an open-source PCAP testing system) which \'93uses Docker containers and can be spun up in a matter of minutes with a single command\'94 . Containerization ensures all dependencies (Suricata, Zeek, parsers) are included and that the environment is reproducible.\
	\'95	Using both 
\f2\b Suricata and Zeek
\f1\b0  leverages the strengths of each: As noted by the PcapMonkey project, it\'92s effective to analyze PCAPs with the latest Suricata 
\f4\i and
\f1\i0  Zeek for comprehensive coverage . Suricata will provide IDS alerts, while Zeek gives a holistic view of traffic; the combination enhances the timeline with both high-fidelity alerts and broad context.\
	\'95	For 
\f2\b Windows Event Log
\f1\b0  parsing, tools like Timesketch have demonstrated converting EVTX into timeline events stored in Elasticsearch . In our offline context, we will similarly parse EVTX to JSON/CSV and include those events. (Another example, PcapMonkey\'92s use of an 
\f3 evtxtoelk
\f1  container shows one method to ingest EVTX into Elastic for analysis  \'96 our app can achieve the same end without requiring a full Elastic stack by parsing to local data structures).\
	\'95	
\f2\b Zeek offline analysis:
\f1\b0  The Zeek documentation highlights that \'93when you want to do offline analysis of already captured PCAP files, Zeek is a very handy tool to analyze the pcap and gives a high level holistic view of the traffic\'94 . This supports our inclusion of Zeek to extract an abundance of network intelligence from the captures.\
	\'95	The importance of 
\f2\b correlating events across multiple sources
\f1\b0  is well-established in digital forensics. Modern log analysis \'93goes beyond simple pattern matching to include time-sequence correlation for identifying causal relationships between events\'85 across multiple log sources\'94 . Our timeline correlation engine is built exactly to fulfill this: aligning IDS alerts, system logs, and attacker actions in sequence to reveal cause-and-effect during the attack.\
	\'95	For the 
\f2\b interactive timeline visualization
\f1\b0 , we leverage existing libraries to avoid reinventing the wheel. The vis.js Timeline, for instance, provides an \'93interactive visualization chart to visualize data in time\'94 where one can \'93move and zoom in the timeline by dragging and scrolling\'94 . Utilizing such a library, embedded in our web UI, will allow the analyst to fluidly explore the timeline of events, which is far more user-friendly and insightful than a static list. This aligns with our recommendation to include an interactive timeline in the HTML interface (with an option for a static report for documentation).}